<template>
  <aside class="w-64 p-4 border-r h-full overflow-y-auto"
         style="background-color: rgba(10, 22, 52, 0.5);
                border-color: rgba(0, 150, 255, 0.2);
                backdrop-filter: blur(5px);">
    <h3 class="text-lg font-bold mb-4 text-white">
      <span class="text-blue-400">组件</span>库
    </h3>
    <div class="space-y-3">
      <div
        v-for="widget in availableWidgets"
        :key="widget.component"
        class="grid-stack-item new-widget p-3 cursor-grab rounded-md border-2 border-dashed transition-all duration-300 hover:scale-105 relative"
        style="background-color: rgba(0, 150, 255, 0.1);
               border-color: rgba(0, 150, 255, 0.5);
               box-shadow: 0 0 10px rgba(0, 150, 255, 0.2);"
        :gs-component="widget.component"
        :gs-props="JSON.stringify(widget.props)"
        :gs-w="widget.w"
        :gs-h="widget.h"
        @mousedown="onWidgetMouseDown"
      >
        <!-- 点击添加按钮 -->
        <button
          @click="addWidget(widget)"
          class="absolute top-1 right-1 w-6 h-6 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center transition-colors z-10"
          title="点击添加到画布"
        >
          <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
        </button>

        <div class="grid-stack-item-content text-center text-white">
          <div class="text-2xl mb-2 text-blue-400">{{ widget.icon }}</div>
          <div class="font-semibold text-white text-sm">{{ widget.props.title }}</div>
          <div class="text-xs text-blue-200 mt-1">{{ widget.description }}</div>
        </div>
      </div>
    </div>
    
    <!-- 使用说明 -->
    <div class="mt-6 p-3 rounded-md border"
         style="background-color: rgba(0, 150, 255, 0.1);
                border-color: rgba(0, 150, 255, 0.3);
                box-shadow: 0 0 8px rgba(0, 150, 255, 0.2);">
      <h4 class="text-sm font-semibold text-blue-300 mb-2">使用说明</h4>
      <ul class="text-xs text-blue-100 space-y-1">
        <li>• 拖拽组件到右侧画布</li>
        <li>• 调整组件大小和位置</li>
        <li>• 点击 ⚙️ 配置组件</li>
        <li>• 点击 × 删除组件</li>
        <li>• 布局自动保存</li>
      </ul>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useLayoutStore } from '@/stores/layout'

const layoutStore = useLayoutStore()

// 可用组件定义
const availableWidgets = ref([
  { 
    component: 'BasicInfoCard', 
    props: { title: '基本信息' }, 
    w: 4, 
    h: 5,
    icon: '📊',
    description: '显示基础数据统计'
  },
  { 
    component: 'SalesChartCard', 
    props: { title: '销售排行' }, 
    w: 4, 
    h: 5,
    icon: '📈',
    description: '销售数据饼图'
  },
  { 
    component: 'KeyMetricCard', 
    props: { title: '核心指标' }, 
    w: 4, 
    h: 10,
    icon: '🎯',
    description: '关键业务指标'
  },
  {
    component: 'MapCard',
    props: { title: '网络分布图', dataSourceId: 'province-sales' },
    w: 6,
    h: 6,
    icon: '🗺️',
    description: '中国地图数据可视化'
  },
  { 
    component: 'SalesChartCard', 
    props: { title: '趋势分析' }, 
    w: 6, 
    h: 6,
    icon: '📉',
    description: '数据趋势图表'
  },
  { 
    component: 'KeyMetricCard', 
    props: { title: '实时监控' }, 
    w: 3, 
    h: 4,
    icon: '⚡',
    description: '实时数据监控'
  }
])

// 处理鼠标按下事件，为拖拽做准备
const onWidgetMouseDown = (event: MouseEvent) => {
  const target = event.currentTarget as HTMLElement
  
  // 添加拖拽开始的视觉反馈
  target.style.opacity = '0.8'
  target.style.transform = 'scale(0.95)'
  
  // 鼠标释放时恢复样式
  const onMouseUp = () => {
    target.style.opacity = '1'
    target.style.transform = 'scale(1)'
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mouseup', onMouseUp)
}

// 点击添加组件到画布
const addWidget = (widget: any) => {
  layoutStore.addWidget(widget.component, widget.props, widget.w, widget.h)
}
</script>

<style scoped>
.new-widget {
  user-select: none;
  transition: all 0.2s ease;
}

.new-widget:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.new-widget:active {
  transform: scale(0.95);
}

/* 确保工具箱在拖拽时不会被选中 */
.grid-stack-item-content {
  pointer-events: none;
}

.new-widget {
  pointer-events: auto;
}
</style>
