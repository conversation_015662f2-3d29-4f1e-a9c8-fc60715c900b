<template>
  <div class="order-list-test">
    <AppHeader title="实时订单列表组件测试" />
    
    <div class="test-container">
      <div class="controls-section">
        <h2>控制面板</h2>
        <div class="controls-grid">
          <div class="control-group">
            <label>数据集选择:</label>
            <select v-model="selectedDataset" @change="switchDataset">
              <option value="default">默认订单</option>
              <option value="busy">繁忙时段</option>
              <option value="minimal">精简数据</option>
              <option value="mixed">混合状态</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>滚动速度:</label>
            <select v-model="scrollSpeed">
              <option :value="60">慢速 (60秒)</option>
              <option :value="30">正常 (30秒)</option>
              <option :value="15">快速 (15秒)</option>
              <option :value="8">极速 (8秒)</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>显示行数:</label>
            <select v-model="maxRows">
              <option :value="6">6行</option>
              <option :value="8">8行</option>
              <option :value="10">10行</option>
              <option :value="12">12行</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="showControls" />
              显示控制按钮
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="isLoading" />
              加载状态
            </label>
          </div>
          
          <div class="control-group">
            <button @click="addRandomOrder" class="action-btn">
              添加随机订单
            </button>
          </div>
          
          <div class="control-group">
            <button @click="refreshData" class="action-btn">
              刷新数据
            </button>
          </div>
        </div>
      </div>

      <div class="lists-section">
        <h2>列表展示</h2>
        <div class="lists-grid">
          <!-- 标准尺寸列表 -->
          <div class="list-wrapper">
            <h3>标准尺寸 (400x500)</h3>
            <div class="list-container standard">
              <RealTimeOrderList
                :listData="currentData"
                :title="currentTitle"
                :subtitle="currentSubtitle"
                :scrollDuration="scrollSpeed"
                :maxRows="maxRows"
                :showControls="showControls"
                :loading="isLoading"
              />
            </div>
          </div>
          
          <!-- 大尺寸列表 -->
          <div class="list-wrapper">
            <h3>大尺寸 (500x600)</h3>
            <div class="list-container large">
              <RealTimeOrderList
                :listData="currentData"
                :title="currentTitle"
                :subtitle="currentSubtitle"
                :scrollDuration="scrollSpeed"
                :maxRows="maxRows + 2"
                :showControls="showControls"
                :loading="isLoading"
              />
            </div>
          </div>
          
          <!-- 小尺寸列表 -->
          <div class="list-wrapper">
            <h3>小尺寸 (300x400)</h3>
            <div class="list-container small">
              <RealTimeOrderList
                :listData="currentData"
                :title="currentTitle"
                :subtitle="currentSubtitle"
                :scrollDuration="scrollSpeed"
                :maxRows="maxRows - 2"
                :showControls="false"
                :loading="isLoading"
              />
            </div>
          </div>
          
          <!-- 实时数据列表 -->
          <div class="list-wrapper">
            <h3>实时数据更新</h3>
            <div class="list-container standard">
              <RealTimeOrderList
                :listData="realtimeData"
                title="实时订单流水"
                subtitle="数据每5秒自动更新"
                :scrollDuration="20"
                :maxRows="8"
                :showControls="true"
                :loading="false"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="data-section">
        <h2>当前数据结构</h2>
        <div class="data-display">
          <div class="data-item">
            <h4>订单数据示例:</h4>
            <pre>{{ JSON.stringify(currentData.slice(0, 3), null, 2) }}...</pre>
          </div>
          <div class="data-item">
            <h4>数据统计:</h4>
            <div class="stats">
              <div class="stat">
                <span class="stat-label">总订单数:</span>
                <span class="stat-value">{{ currentData.length }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">运输中:</span>
                <span class="stat-value">{{ getStatusCount('运输中') }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">配送中:</span>
                <span class="stat-value">{{ getStatusCount('配送中') }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">已送达:</span>
                <span class="stat-value">{{ getStatusCount('已送达') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppHeader from './components/AppHeader.vue'
import RealTimeOrderList from './components/RealTimeOrderList.vue'

// 响应式数据
const selectedDataset = ref('default')
const scrollSpeed = ref(30)
const maxRows = ref(8)
const showControls = ref(false)
const isLoading = ref(false)

// 预设数据集
const datasets = {
  default: {
    title: '实时订单流水',
    subtitle: '全国物流订单实时监控',
    data: [
      { orderId: 'SH20240715001', origin: '上海仓', destination: '北京分拨中心', status: '运输中' },
      { orderId: 'BJ20240715002', origin: '北京仓', destination: '广州分拨中心', status: '已发货' },
      { orderId: 'GZ20240715003', origin: '广州仓', destination: '深圳配送站', status: '配送中' },
      { orderId: 'SZ20240715004', origin: '深圳仓', destination: '上海分拨中心', status: '运输中' },
      { orderId: 'WH20240715005', origin: '武汉仓', destination: '成都分拨中心', status: '已发货' },
      { orderId: 'CD20240715006', origin: '成都仓', destination: '西安配送站', status: '配送中' },
      { orderId: 'XA20240715007', origin: '西安仓', destination: '武汉分拨中心', status: '运输中' },
      { orderId: 'HZ20240715008', origin: '杭州仓', destination: '上海配送站', status: '已送达' },
      { orderId: 'NJ20240715009', origin: '南京仓', destination: '杭州分拨中心', status: '运输中' },
      { orderId: 'TJ20240715010', origin: '天津仓', destination: '北京配送站', status: '配送中' },
      { orderId: 'CQ20240715011', origin: '重庆仓', destination: '成都分拨中心', status: '已发货' },
      { orderId: 'QD20240715012', origin: '青岛仓', destination: '天津分拨中心', status: '运输中' }
    ]
  },
  busy: {
    title: '繁忙时段订单',
    subtitle: '双11购物节订单高峰',
    data: [
      { orderId: 'DB20241111001', origin: '上海仓', destination: '北京配送站', status: '运输中' },
      { orderId: 'DB20241111002', origin: '广州仓', destination: '深圳配送站', status: '配送中' },
      { orderId: 'DB20241111003', origin: '北京仓', destination: '天津配送站', status: '运输中' },
      { orderId: 'DB20241111004', origin: '武汉仓', destination: '长沙配送站', status: '配送中' },
      { orderId: 'DB20241111005', origin: '成都仓', destination: '重庆配送站', status: '已发货' },
      { orderId: 'DB20241111006', origin: '西安仓', destination: '兰州配送站', status: '运输中' },
      { orderId: 'DB20241111007', origin: '杭州仓', destination: '宁波配送站', status: '配送中' },
      { orderId: 'DB20241111008', origin: '南京仓', destination: '苏州配送站', status: '已送达' },
      { orderId: 'DB20241111009', origin: '青岛仓', destination: '济南配送站', status: '运输中' },
      { orderId: 'DB20241111010', origin: '大连仓', destination: '沈阳配送站', status: '配送中' },
      { orderId: 'DB20241111011', origin: '福州仓', destination: '厦门配送站', status: '已发货' },
      { orderId: 'DB20241111012', origin: '昆明仓', destination: '贵阳配送站', status: '运输中' },
      { orderId: 'DB20241111013', origin: '乌鲁木齐仓', destination: '石河子配送站', status: '配送中' },
      { orderId: 'DB20241111014', origin: '拉萨仓', destination: '日喀则配送站', status: '已发货' },
      { orderId: 'DB20241111015', origin: '海口仓', destination: '三亚配送站', status: '运输中' }
    ]
  },
  minimal: {
    title: '精简订单监控',
    subtitle: '核心路线订单',
    data: [
      { orderId: 'MIN20240715001', origin: '北京仓', destination: '上海分拨中心', status: '运输中' },
      { orderId: 'MIN20240715002', origin: '上海仓', destination: '广州分拨中心', status: '配送中' },
      { orderId: 'MIN20240715003', origin: '广州仓', destination: '深圳配送站', status: '已送达' },
      { orderId: 'MIN20240715004', origin: '深圳仓', destination: '北京分拨中心', status: '运输中' },
      { orderId: 'MIN20240715005', origin: '武汉仓', destination: '成都分拨中心', status: '已发货' }
    ]
  },
  mixed: {
    title: '混合状态订单',
    subtitle: '包含各种订单状态',
    data: [
      { orderId: 'MIX20240715001', origin: '上海仓', destination: '北京配送站', status: '已送达' },
      { orderId: 'MIX20240715002', origin: '北京仓', destination: '广州配送站', status: '异常' },
      { orderId: 'MIX20240715003', origin: '广州仓', destination: '深圳配送站', status: '配送中' },
      { orderId: 'MIX20240715004', origin: '深圳仓', destination: '上海配送站', status: '运输中' },
      { orderId: 'MIX20240715005', origin: '武汉仓', destination: '成都配送站', status: '已发货' },
      { orderId: 'MIX20240715006', origin: '成都仓', destination: '西安配送站', status: '已送达' },
      { orderId: 'MIX20240715007', origin: '西安仓', destination: '武汉配送站', status: '异常' },
      { orderId: 'MIX20240715008', origin: '杭州仓', destination: '南京配送站', status: '配送中' }
    ]
  }
}

// 实时数据（模拟动态更新）
const realtimeData = ref([
  { orderId: 'RT20240715001', origin: '上海仓', destination: '北京配送站', status: '运输中' },
  { orderId: 'RT20240715002', origin: '北京仓', destination: '广州配送站', status: '配送中' },
  { orderId: 'RT20240715003', origin: '广州仓', destination: '深圳配送站', status: '已发货' },
  { orderId: 'RT20240715004', origin: '武汉仓', destination: '成都配送站', status: '运输中' },
  { orderId: 'RT20240715005', origin: '成都仓', destination: '西安配送站', status: '配送中' }
])

// 当前数据
const currentData = computed(() => datasets[selectedDataset.value].data)
const currentTitle = computed(() => datasets[selectedDataset.value].title)
const currentSubtitle = computed(() => datasets[selectedDataset.value].subtitle)

// 统计方法
const getStatusCount = (status) => {
  return currentData.value.filter(order => order.status === status).length
}

// 切换数据集
const switchDataset = () => {
  console.log('切换到数据集:', selectedDataset.value)
}

// 添加随机订单
const addRandomOrder = () => {
  const origins = ['上海仓', '北京仓', '广州仓', '深圳仓', '武汉仓', '成都仓', '西安仓', '杭州仓']
  const destinations = ['北京配送站', '上海配送站', '广州配送站', '深圳配送站', '武汉配送站', '成都配送站']
  const statuses = ['已发货', '运输中', '配送中', '已送达']
  
  const randomOrder = {
    orderId: `RND${Date.now()}`,
    origin: origins[Math.floor(Math.random() * origins.length)],
    destination: destinations[Math.floor(Math.random() * destinations.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)]
  }
  
  datasets[selectedDataset.value].data.unshift(randomOrder)
  console.log('添加新订单:', randomOrder.orderId)
}

// 刷新数据
const refreshData = () => {
  // 随机更新订单状态
  datasets[selectedDataset.value].data.forEach(order => {
    if (Math.random() > 0.8) {
      const statuses = ['已发货', '运输中', '配送中', '已送达']
      order.status = statuses[Math.floor(Math.random() * statuses.length)]
    }
  })
  console.log('数据已刷新')
}

// 实时数据更新
const updateRealtimeData = () => {
  const statuses = ['已发货', '运输中', '配送中', '已送达']
  realtimeData.value.forEach(order => {
    if (Math.random() > 0.7) {
      order.status = statuses[Math.floor(Math.random() * statuses.length)]
    }
  })
  
  // 偶尔添加新订单
  if (Math.random() > 0.8) {
    const newOrder = {
      orderId: `RT${Date.now()}`,
      origin: '实时仓',
      destination: '实时配送站',
      status: statuses[Math.floor(Math.random() * statuses.length)]
    }
    realtimeData.value.unshift(newOrder)
    if (realtimeData.value.length > 10) {
      realtimeData.value.pop()
    }
  }
}

let realtimeInterval = null

onMounted(() => {
  // 启动实时数据更新
  realtimeInterval = setInterval(updateRealtimeData, 5000)
  console.log('实时订单列表测试页面已加载')
})

onUnmounted(() => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
  }
})
</script>

<style scoped>
.order-list-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
}

.controls-section,
.lists-section,
.data-section {
  margin-bottom: 40px;
}

.controls-section h2,
.lists-section h2,
.data-section h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  background: rgba(14, 38, 92, 0.6);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #1E90FF;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  color: #7BDEFF;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group select {
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid #1E90FF;
  color: #ffffff;
  padding: 8px;
  border-radius: 4px;
}

.control-group input[type="checkbox"] {
  accent-color: #1E90FF;
}

.action-btn {
  background: rgba(0, 255, 127, 0.8);
  color: #ffffff;
  border: 1px solid #00FF7F;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.action-btn:hover {
  background: rgba(0, 255, 127, 1);
  transform: translateY(-2px);
}

.lists-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.list-wrapper {
  background: rgba(14, 38, 92, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.list-wrapper h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1rem;
}

.list-container {
  border-radius: 8px;
  overflow: hidden;
}

.list-container.standard {
  height: 500px;
}

.list-container.large {
  height: 600px;
}

.list-container.small {
  height: 400px;
}

.data-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.data-item {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
}

.data-item h4 {
  color: #7BDEFF;
  margin-bottom: 10px;
  font-size: 1rem;
}

.data-item pre {
  color: #7BDEFF;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  margin: 0;
  white-space: pre-wrap;
  overflow-x: auto;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: rgba(30, 144, 255, 0.1);
  border-radius: 4px;
}

.stat-label {
  color: #7BDEFF;
  font-size: 0.9rem;
}

.stat-value {
  color: #00FF7F;
  font-weight: bold;
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .lists-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .controls-grid {
    grid-template-columns: 1fr;
  }
  
  .data-display {
    grid-template-columns: 1fr;
  }
  
  .lists-grid {
    grid-template-columns: 1fr;
  }
}
</style>
