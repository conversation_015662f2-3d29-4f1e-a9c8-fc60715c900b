// src/stores/filterStore.ts

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

interface FilterState {
  province: string | null;
}

export const useFilterStore = defineStore('filter', () => {
  // state
  const currentFilter = ref<FilterState>({
    province: null, // 我们以省份为例
  });

  // getters
  const hasFilter = computed(() => !!currentFilter.value.province);
  const filterSummary = computed(() => {
    if (hasFilter.value) {
      return `当前筛选: ${currentFilter.value.province}`;
    }
    return '当前无筛选';
  });

  // actions
  function setProvinceFilter(provinceName: string) {
    console.log(`[FilterStore] 设置筛选: ${provinceName}`);
    currentFilter.value = { province: provinceName };
  }

  function setFilter(newFilter: string) {
    console.log(`[FilterStore] 设置筛选: ${newFilter}`);
    currentFilter.value = { province: newFilter };
  }

  function clearFilter() {
    console.log('[FilterStore] 清除筛选');
    currentFilter.value = { province: null };
  }

  return { currentFilter, hasFilter, filterSummary, setProvinceFilter, setFilter, clearFilter };
});
