// src/stores/filterStore.ts

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

interface FilterState {
  [key: string]: string | null;
}

export const useFilterStore = defineStore('filter', () => {
  // state - 从单一值升级为一个对象，用于存储所有筛选条件
  const filters = ref<FilterState>({});

  // getters
  const hasFilter = computed(() => Object.keys(filters.value).length > 0);
  const filterSummary = computed(() => {
    const activeFilters = Object.entries(filters.value)
      .filter(([_, value]) => value !== null && value !== '')
      .map(([key, value]) => `${key}: ${value}`);

    if (activeFilters.length > 0) {
      return `当前筛选: ${activeFilters.join(', ')}`;
    }
    return '当前无筛选';
  });

  // actions
  function setFilter({ key, value }: { key: string; value: string | null }) {
    console.log(`[FilterStore] 设置筛选 ${key}: ${value}`);
    if (value) {
      filters.value[key] = value;
    } else {
      // 如果 value 为 null 或 undefined，则移除该筛选条件
      clearFilter(key);
    }
  }

  function clearFilter(key?: string) {
    if (key) {
      console.log(`[FilterStore] 清除筛选: ${key}`);
      delete filters.value[key];
    } else {
      console.log('[FilterStore] 清除所有筛选');
      // 如果不提供 key，则清空所有筛选
      filters.value = {};
    }
  }

  // 保持向后兼容的方法
  function setProvinceFilter(provinceName: string) {
    setFilter({ key: 'province', value: provinceName });
  }

  return {
    filters,
    hasFilter,
    filterSummary,
    setFilter,
    clearFilter,
    setProvinceFilter,
    // 为了向后兼容，保留旧的接口
    currentFilter: computed(() => ({ province: filters.value.province || null }))
  };
});
