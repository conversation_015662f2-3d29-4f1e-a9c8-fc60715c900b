<template>
  <div class="enhanced-chart-container" :id="chartId" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: '商品分类占比'
  },
  chartId: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  }
})

// 图表实例和容器引用
const chartInstance = ref(null)
const chartContainer = ref(null)

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance.value = echarts.init(chartContainer.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance.value) return

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: 'rgba(6, 22, 74, 0.9)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      extraCssText: 'box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);'
    },
    legend: {
      orient: 'vertical',
      left: '5%',
      top: 'center',
      textStyle: {
        color: '#7BDEFF',
        fontSize: 11,
        fontWeight: '500'
      },
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 8,
      icon: 'circle'
    },
    series: [{
      name: props.title,
      type: 'pie',
      radius: ['35%', '75%'],
      center: ['65%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        color: '#7BDEFF',
        fontSize: 10,
        formatter: '{b}\n{d}%',
        lineHeight: 12
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 12,
          fontWeight: 'bold',
          color: '#00FF88'
        },
        itemStyle: {
          shadowBlur: 15,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 212, 255, 0.8)',
          borderColor: '#00D4FF',
          borderWidth: 3
        },
        scale: true,
        scaleSize: 5
      },
      labelLine: {
        show: true,
        lineStyle: {
          color: 'rgba(123, 222, 255, 0.6)',
          width: 1
        },
        length: 10,
        length2: 15
      },
      data: props.data.length > 0 ? props.data : getDefaultData(),
      itemStyle: {
        borderRadius: 8,
        borderColor: 'rgba(6, 22, 74, 0.8)',
        borderWidth: 2,
        shadowBlur: 10,
        shadowColor: function(params) {
          const colors = ['#00D4FF', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
          return colors[params.dataIndex % colors.length];
        }
      },
      animationType: 'scale',
      animationEasing: 'elasticOut',
      animationDelay: function (idx) {
        return Math.random() * 200;
      }
    }],
    color: [
      {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#00D4FF' },
          { offset: 1, color: '#0096FF' }
        ]
      },
      {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#4ECDC4' },
          { offset: 1, color: '#26A69A' }
        ]
      },
      {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#45B7D1' },
          { offset: 1, color: '#2196F3' }
        ]
      },
      {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#96CEB4' },
          { offset: 1, color: '#4CAF50' }
        ]
      },
      {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#FFEAA7' },
          { offset: 1, color: '#FDCB6E' }
        ]
      },
      {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 1,
        colorStops: [
          { offset: 0, color: '#DDA0DD' },
          { offset: 1, color: '#BA68C8' }
        ]
      }
    ]
  }

  chartInstance.value.setOption(option, true)
}

// 默认数据
const getDefaultData = () => [
  { value: 35, name: '电子产品' },
  { value: 25, name: '服装鞋帽' },
  { value: 20, name: '食品饮料' },
  { value: 12, name: '家居用品' },
  { value: 8, name: '其他' }
]

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 组件挂载
onMounted(() => {
  initChart()
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法供父组件调用
defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
.enhanced-chart-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
  position: relative;
  background: radial-gradient(circle at center, rgba(0, 212, 255, 0.03) 0%, transparent 70%);
  border-radius: 8px;
}
</style>
