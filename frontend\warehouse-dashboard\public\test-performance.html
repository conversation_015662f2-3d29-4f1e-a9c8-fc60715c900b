<!DOCTYPE html>
<html>
<head>
    <title>性能和稳定性测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; min-width: 150px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 12px; color: #666; }
        .test-button { padding: 10px 20px; margin: 5px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #218838; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #007bff; transition: width 0.3s ease; }
        #test-log { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>⚡ 性能和稳定性测试</h1>
    
    <div class="test-section">
        <h2>实时性能指标</h2>
        <div id="performance-metrics">
            <div class="metric">
                <div class="metric-value" id="memory-usage">--</div>
                <div class="metric-label">内存使用 (MB)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="load-time">--</div>
                <div class="metric-label">页面加载时间 (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="fps">--</div>
                <div class="metric-label">帧率 (FPS)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="dom-nodes">--</div>
                <div class="metric-label">DOM 节点数</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>性能测试</h2>
        <button class="test-button" onclick="runPerformanceTest()">🚀 运行性能测试</button>
        <button class="test-button" onclick="runStressTest()">💪 运行压力测试</button>
        <button class="test-button" onclick="runMemoryTest()">🧠 运行内存测试</button>
        <button class="test-button" onclick="clearLog()">🗑️ 清除日志</button>
        
        <div style="margin-top: 15px;">
            <div>测试进度:</div>
            <div class="progress-bar">
                <div class="progress-fill" id="test-progress" style="width: 0%;"></div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>测试日志</h2>
        <div id="test-log"></div>
    </div>

    <script>
        let testLog = [];
        let performanceMonitor = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push(`[${timestamp}] ${message}`);
            updateLogDisplay();
        }
        
        function updateLogDisplay() {
            const logElement = document.getElementById('test-log');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            testLog = [];
            updateLogDisplay();
        }
        
        function updateProgress(percent) {
            document.getElementById('test-progress').style.width = percent + '%';
        }
        
        function updateMetrics() {
            // 内存使用
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memory-usage').textContent = memoryMB;
            }
            
            // DOM 节点数
            const domNodes = document.getElementsByTagName('*').length;
            document.getElementById('dom-nodes').textContent = domNodes;
            
            // 页面加载时间
            if (performance.timing) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                if (loadTime > 0) {
                    document.getElementById('load-time').textContent = loadTime;
                }
            }
        }
        
        function measureFPS() {
            let frames = 0;
            let lastTime = performance.now();
            
            function countFrame() {
                frames++;
                const currentTime = performance.now();
                
                if (currentTime - lastTime >= 1000) {
                    document.getElementById('fps').textContent = frames;
                    frames = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(countFrame);
            }
            
            requestAnimationFrame(countFrame);
        }
        
        async function runPerformanceTest() {
            log('🚀 开始性能测试...');
            updateProgress(0);
            
            try {
                // 测试1: 页面加载性能
                updateProgress(20);
                log('📊 测试页面加载性能...');
                await testPageLoadPerformance();
                
                // 测试2: 资源加载性能
                updateProgress(40);
                log('📦 测试资源加载性能...');
                await testResourceLoadPerformance();
                
                // 测试3: 渲染性能
                updateProgress(60);
                log('🎨 测试渲染性能...');
                await testRenderPerformance();
                
                // 测试4: 交互响应性能
                updateProgress(80);
                log('🖱️ 测试交互响应性能...');
                await testInteractionPerformance();
                
                updateProgress(100);
                log('✅ 性能测试完成！');
                
            } catch (error) {
                log(`❌ 性能测试失败: ${error.message}`);
            }
        }
        
        async function testPageLoadPerformance() {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                log(`📈 DNS查询时间: ${Math.round(navigation.domainLookupEnd - navigation.domainLookupStart)}ms`);
                log(`📈 TCP连接时间: ${Math.round(navigation.connectEnd - navigation.connectStart)}ms`);
                log(`📈 首字节时间: ${Math.round(navigation.responseStart - navigation.requestStart)}ms`);
                log(`📈 DOM构建时间: ${Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)}ms`);
                log(`📈 页面完全加载时间: ${Math.round(navigation.loadEventEnd - navigation.loadEventStart)}ms`);
            }
        }
        
        async function testResourceLoadPerformance() {
            const resources = performance.getEntriesByType('resource');
            const slowResources = resources.filter(r => r.duration > 1000);
            
            log(`📦 总资源数量: ${resources.length}`);
            log(`📦 慢资源数量 (>1s): ${slowResources.length}`);
            
            if (slowResources.length > 0) {
                slowResources.forEach(resource => {
                    log(`⚠️ 慢资源: ${resource.name.split('/').pop()} (${Math.round(resource.duration)}ms)`);
                });
            }
            
            // 测试主应用资源
            try {
                const startTime = performance.now();
                const response = await fetch('http://localhost:5175/');
                const endTime = performance.now();
                log(`📦 主应用响应时间: ${Math.round(endTime - startTime)}ms`);
            } catch (error) {
                log(`❌ 主应用访问失败: ${error.message}`);
            }
        }
        
        async function testRenderPerformance() {
            const startTime = performance.now();
            
            // 创建测试元素
            const testContainer = document.createElement('div');
            testContainer.style.position = 'absolute';
            testContainer.style.top = '-9999px';
            document.body.appendChild(testContainer);
            
            // 测试大量DOM操作
            for (let i = 0; i < 1000; i++) {
                const element = document.createElement('div');
                element.textContent = `Test element ${i}`;
                testContainer.appendChild(element);
            }
            
            const endTime = performance.now();
            log(`🎨 1000个DOM元素创建时间: ${Math.round(endTime - startTime)}ms`);
            
            // 清理
            document.body.removeChild(testContainer);
        }
        
        async function testInteractionPerformance() {
            const testTimes = [];
            
            for (let i = 0; i < 10; i++) {
                const startTime = performance.now();
                
                // 模拟点击事件
                const event = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                
                document.body.dispatchEvent(event);
                
                const endTime = performance.now();
                testTimes.push(endTime - startTime);
                
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            
            const avgTime = testTimes.reduce((a, b) => a + b, 0) / testTimes.length;
            log(`🖱️ 平均事件响应时间: ${avgTime.toFixed(2)}ms`);
        }
        
        async function runStressTest() {
            log('💪 开始压力测试...');
            updateProgress(0);
            
            try {
                // 压力测试1: 大量数据渲染
                updateProgress(25);
                log('📊 测试大量数据渲染...');
                await stressTestDataRendering();
                
                // 压力测试2: 频繁DOM操作
                updateProgress(50);
                log('🔄 测试频繁DOM操作...');
                await stressTestDOMOperations();
                
                // 压力测试3: 内存压力
                updateProgress(75);
                log('🧠 测试内存压力...');
                await stressTestMemory();
                
                updateProgress(100);
                log('✅ 压力测试完成！');
                
            } catch (error) {
                log(`❌ 压力测试失败: ${error.message}`);
            }
        }
        
        async function stressTestDataRendering() {
            const startTime = performance.now();
            const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            // 创建大量数据
            const largeDataSet = Array.from({ length: 10000 }, (_, i) => ({
                id: i,
                name: `Item ${i}`,
                value: Math.random() * 1000,
                timestamp: new Date().toISOString()
            }));
            
            const endTime = performance.now();
            const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            log(`📊 10000条数据创建时间: ${Math.round(endTime - startTime)}ms`);
            log(`📊 内存增长: ${Math.round((endMemory - startMemory) / 1024 / 1024)}MB`);
        }
        
        async function stressTestDOMOperations() {
            const operations = 5000;
            const startTime = performance.now();
            
            const container = document.createElement('div');
            container.style.position = 'absolute';
            container.style.top = '-9999px';
            document.body.appendChild(container);
            
            for (let i = 0; i < operations; i++) {
                const element = document.createElement('div');
                element.textContent = `Stress test ${i}`;
                container.appendChild(element);
                
                if (i % 2 === 0) {
                    element.style.color = 'red';
                } else {
                    element.style.color = 'blue';
                }
                
                if (i % 100 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 1));
                }
            }
            
            const endTime = performance.now();
            log(`🔄 ${operations}次DOM操作时间: ${Math.round(endTime - startTime)}ms`);
            
            document.body.removeChild(container);
        }
        
        async function stressTestMemory() {
            const arrays = [];
            const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            // 创建大量数组占用内存
            for (let i = 0; i < 100; i++) {
                arrays.push(new Array(10000).fill(Math.random()));
                
                if (i % 10 === 0) {
                    const currentMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                    log(`🧠 内存使用: ${Math.round(currentMemory / 1024 / 1024)}MB`);
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            }
            
            const peakMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            log(`🧠 峰值内存: ${Math.round(peakMemory / 1024 / 1024)}MB`);
            
            // 清理内存
            arrays.length = 0;
            
            // 强制垃圾回收（如果可用）
            if (window.gc) {
                window.gc();
            }
            
            setTimeout(() => {
                const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                log(`🧠 清理后内存: ${Math.round(finalMemory / 1024 / 1024)}MB`);
            }, 1000);
        }
        
        async function runMemoryTest() {
            log('🧠 开始内存测试...');
            
            if (!performance.memory) {
                log('❌ 浏览器不支持内存监控');
                return;
            }
            
            const initialMemory = performance.memory.usedJSHeapSize;
            log(`🧠 初始内存: ${Math.round(initialMemory / 1024 / 1024)}MB`);
            
            // 内存泄漏测试
            const leakTest = [];
            for (let i = 0; i < 1000; i++) {
                leakTest.push({
                    data: new Array(1000).fill(Math.random()),
                    timestamp: Date.now()
                });
            }
            
            const afterAllocation = performance.memory.usedJSHeapSize;
            log(`🧠 分配后内存: ${Math.round(afterAllocation / 1024 / 1024)}MB`);
            log(`🧠 内存增长: ${Math.round((afterAllocation - initialMemory) / 1024 / 1024)}MB`);
            
            // 清理并检查是否有内存泄漏
            leakTest.length = 0;
            
            setTimeout(() => {
                const finalMemory = performance.memory.usedJSHeapSize;
                const memoryDiff = finalMemory - initialMemory;
                log(`🧠 清理后内存: ${Math.round(finalMemory / 1024 / 1024)}MB`);
                
                if (memoryDiff > 1024 * 1024) { // 1MB
                    log(`⚠️ 可能存在内存泄漏: ${Math.round(memoryDiff / 1024 / 1024)}MB`);
                } else {
                    log(`✅ 内存管理正常`);
                }
            }, 2000);
        }
        
        // 初始化
        window.onload = function() {
            log('⚡ 性能测试工具已就绪');
            updateMetrics();
            measureFPS();
            
            // 定期更新指标
            performanceMonitor = setInterval(updateMetrics, 1000);
        };
        
        window.onbeforeunload = function() {
            if (performanceMonitor) {
                clearInterval(performanceMonitor);
            }
        };
    </script>
</body>
</html>
