/**
 * 核心数据类型定义
 * 为整个应用提供类型安全保障
 */

// ============= 基础数据类型 =============

/**
 * KPI指标数据项
 */
export interface KeyMetric {
  title: string
  value: number
  unit: string
  trend: number
  icon: string
  type?: 'primary' | 'success' | 'warning' | 'info'
  trendLabel?: string
  description?: string
  loading?: boolean
  precision?: number
}

/**
 * KPI指标数据数组
 */
export type KeyMetrics = KeyMetric[]

/**
 * 销售分布数据项（人力成本数据项）
 */
export interface SalesDistributionItem {
  name: string
  value: number
}

/**
 * 销售分布数据数组
 */
export type SalesDistribution = SalesDistributionItem[]

/**
 * 月度订单数据
 */
export interface MonthlyOrders {
  xAxisData: string[]
  seriesData: number[]
  dates: string[] // 添加日期数组，YYYY-MM-DD 格式
}

/**
 * 物流点数据（仓库数据）
 */
export interface LogisticsPoint {
  name: string
  value: [number, number, number] // [经度, 纬度, 业务量]
  date: string // 添加日期字段，YYYY-MM-DD 格式
}

/**
 * 物流路线数据
 */
export interface LogisticsRoute {
  fromName: string
  toName: string
  coords: [[number, number], [number, number]] // [[起点经度, 起点纬度], [终点经度, 终点纬度]]
  date: string // 添加日期字段，YYYY-MM-DD 格式
  volume: number // 添加运输量字段
}

/**
 * 物流数据
 */
export interface LogisticsData {
  warehouseData: LogisticsPoint[]
  routeData: LogisticsRoute[]
}

/**
 * 实时订单数据
 */
export interface RealTimeOrder {
  orderId: string
  origin: string
  destination: string
  status: '已发货' | '运输中' | '配送中' | '已送达' | '异常'
}

/**
 * 实时订单数据数组
 */
export type RealTimeOrders = RealTimeOrder[]

// ============= 区域分组数据类型 =============

/**
 * 区域名称类型
 */
export type RegionName = '华东区' | '华南区' | '华北区' | '华中区' | '西南区' | '总计'

/**
 * 区域分组数据泛型
 * 用于表示按区域分组的数据结构
 */
export type RegionGroupedData<T> = {
  [K in RegionName]: T
}

/**
 * 按区域分组的KPI数据
 */
export type RegionGroupedKeyMetrics = RegionGroupedData<KeyMetrics>

/**
 * 按区域分组的月度订单数据
 */
export type RegionGroupedMonthlyOrders = RegionGroupedData<MonthlyOrders>

/**
 * 按区域分组的物流数据
 */
export type RegionGroupedLogisticsData = RegionGroupedData<LogisticsData>

// ============= Store 状态类型 =============

/**
 * 日期范围接口
 */
export interface DateRange {
  start: string | null // YYYY-MM-DD 格式
  end: string | null   // YYYY-MM-DD 格式
}

/**
 * Dashboard Store 状态接口
 */
export interface DashboardState {
  // 加载和错误状态
  isLoading: boolean
  error: string | null

  // API数据
  keyMetricsData: RegionGroupedKeyMetrics | null
  salesDistributionData: SalesDistribution | null
  monthlyOrdersData: RegionGroupedMonthlyOrders | null
  logisticsData: RegionGroupedLogisticsData | null
  realTimeOrdersData: RealTimeOrders | null

  // 筛选状态
  selectedRegion: RegionName
  startDate: string | null // 日期范围筛选 - 开始日期
  endDate: string | null   // 日期范围筛选 - 结束日期

  // 时间相关状态
  currentTime: string
  lastUpdateTime: string
  onlineWarehouses: number
  totalWarehouses: number

  // 实时更新订阅
  unsubscribeRealTimeUpdates: (() => void) | null
}

// ============= API 响应类型 =============

/**
 * API 响应基础接口
 */
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  timestamp?: number
}

/**
 * 数据完整性状态
 */
export interface DataCompleteness {
  loaded: number
  total: number
  percentage: number
}

/**
 * 仓库状态信息
 */
export interface WarehouseStatus {
  online: number
  total: number
  percentage: number
}

/**
 * 选中区域信息
 */
export interface SelectedRegionInfo {
  name: RegionName
  isTotal: boolean
}

// ============= 组件 Props 类型 =============

/**
 * KPI卡片组件 Props
 */
export interface KpiCardProps {
  title: string
  value: number
  unit: string
  type?: 'primary' | 'success' | 'warning' | 'info'
  icon?: string
  trend?: number
  trendLabel?: string
  description?: string
  loading?: boolean
  precision?: number
}

/**
 * 图表组件基础 Props
 */
export interface BaseChartProps {
  title?: string
  subtitle?: string
  loading?: boolean
  enableAnimation?: boolean
  chartHeight?: string
}

/**
 * 饼图组件 Props
 */
export interface PieChartProps extends BaseChartProps {
  chartData: SalesDistribution
  showPercentage?: boolean
  themeColor?: string
}

/**
 * 折线图组件 Props
 */
export interface LineChartProps extends BaseChartProps {
  chartData: MonthlyOrders
  showLabel?: boolean
  themeColor?: string
}

/**
 * 地图组件 Props
 */
export interface MapProps extends BaseChartProps {
  warehouseData: LogisticsPoint[]
  routeData: LogisticsRoute[]
}

/**
 * 订单列表组件 Props
 */
export interface OrderListProps {
  listData: RealTimeOrders
  title?: string
  subtitle?: string
  scrollDuration?: number
  maxRows?: number
  showControls?: boolean
  loading?: boolean
}

// ============= 事件类型 =============

/**
 * 实时更新事件类型
 */
export interface RealTimeUpdateEvent {
  type: 'orders' | 'metrics' | 'logistics'
  data: any
  timestamp: number
}

/**
 * 区域选择事件类型
 */
export interface RegionSelectEvent {
  region: RegionName
  timestamp: number
}

// ============= 工具类型 =============

/**
 * 可选字段类型工具
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * 深度只读类型工具
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 提取数组元素类型工具
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

// ============= 常量类型 =============

/**
 * 订单状态常量
 */
export const ORDER_STATUS = {
  SHIPPED: '已发货',
  IN_TRANSIT: '运输中',
  DELIVERING: '配送中',
  DELIVERED: '已送达',
  EXCEPTION: '异常'
} as const

/**
 * 订单状态类型
 */
export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS]

/**
 * 区域常量
 */
export const REGIONS = {
  EAST: '华东区',
  SOUTH: '华南区',
  NORTH: '华北区',
  CENTRAL: '华中区',
  SOUTHWEST: '西南区',
  TOTAL: '总计'
} as const

/**
 * KPI类型常量
 */
export const KPI_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  INFO: 'info'
} as const

/**
 * KPI类型
 */
export type KpiType = typeof KPI_TYPES[keyof typeof KPI_TYPES]
