<template>
  <div class="dashboard-test">
    <!-- 全屏切换控制 -->
    <div class="test-controls" v-if="!isFullscreen">
      <button @click="toggleFullscreen" class="fullscreen-btn">
        进入全屏模式
      </button>
      <button @click="toggleDemo" class="demo-btn">
        {{ isDemoMode ? '停止演示' : '开始演示' }}
      </button>
      <div class="resolution-info">
        当前分辨率: {{ screenResolution }}
      </div>
    </div>
    
    <!-- 全屏退出提示 -->
    <div class="fullscreen-tip" v-if="isFullscreen">
      <span>按 ESC 键退出全屏</span>
    </div>
    
    <!-- 物流大屏组件 -->
    <LogisticsDashboard />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import LogisticsDashboard from './components/LogisticsDashboard.vue'

// 响应式数据
const isFullscreen = ref(false)
const isDemoMode = ref(false)
const screenResolution = ref('')

// 更新屏幕分辨率信息
const updateResolution = () => {
  screenResolution.value = `${window.innerWidth} × ${window.innerHeight}`
}

// 全屏切换
const toggleFullscreen = async () => {
  try {
    if (!document.fullscreenElement) {
      await document.documentElement.requestFullscreen()
      isFullscreen.value = true
    } else {
      await document.exitFullscreen()
      isFullscreen.value = false
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
  }
}

// 演示模式切换
const toggleDemo = () => {
  isDemoMode.value = !isDemoMode.value
  if (isDemoMode.value) {
    startDemo()
  } else {
    stopDemo()
  }
}

let demoInterval = null

// 开始演示模式
const startDemo = () => {
  console.log('开始演示模式')
  // 这里可以添加演示逻辑，比如自动切换数据、动画效果等
  demoInterval = setInterval(() => {
    // 模拟数据变化
    console.log('演示数据更新...')
  }, 5000)
}

// 停止演示模式
const stopDemo = () => {
  console.log('停止演示模式')
  if (demoInterval) {
    clearInterval(demoInterval)
    demoInterval = null
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 监听键盘事件
const handleKeydown = (event) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    isFullscreen.value = false
  }
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  }
}

onMounted(() => {
  // 初始化分辨率信息
  updateResolution()
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateResolution)
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 监听键盘事件
  document.addEventListener('keydown', handleKeydown)
  
  console.log('Dashboard Test 页面已加载')
})

onUnmounted(() => {
  // 清理事件监听器和定时器
  window.removeEventListener('resize', updateResolution)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeydown)
  
  if (demoInterval) {
    clearInterval(demoInterval)
  }
})
</script>

<style scoped>
.dashboard-test {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* 测试控制面板 */
.test-controls {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1000;
  display: flex;
  gap: 1rem;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.fullscreen-btn,
.demo-btn {
  background: rgba(30, 144, 255, 0.8);
  color: #ffffff;
  border: 1px solid #1E90FF;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.fullscreen-btn:hover,
.demo-btn:hover {
  background: rgba(30, 144, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.3);
}

.demo-btn {
  background: rgba(0, 255, 127, 0.8);
  border-color: #00FF7F;
}

.demo-btn:hover {
  background: rgba(0, 255, 127, 1);
  box-shadow: 0 2px 8px rgba(0, 255, 127, 0.3);
}

.resolution-info {
  color: #7BDEFF;
  font-size: 0.75rem;
  font-family: 'Courier New', monospace;
  background: rgba(14, 38, 92, 0.6);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid rgba(30, 144, 255, 0.3);
}

/* 全屏提示 */
.fullscreen-tip {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  color: #7BDEFF;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(30, 144, 255, 0.3);
  animation: fadeInOut 3s ease-in-out infinite;
}

/* 动画效果 */
@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* 确保大屏组件占满全屏 */
.dashboard-test :deep(.logistics-dashboard) {
  width: 100vw;
  height: 100vh;
}

/* 响应式隐藏控制面板 */
@media (max-width: 768px) {
  .test-controls {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
  }
  
  .resolution-info {
    font-size: 0.625rem;
  }
}

/* 全屏模式下的样式调整 */
.dashboard-test:fullscreen {
  background: #000;
}

.dashboard-test:fullscreen .test-controls {
  display: none;
}
</style>
