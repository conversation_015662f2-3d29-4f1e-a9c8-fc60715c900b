<!DOCTYPE html>
<html>
<head>
    <title>地图调试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.6.0/dist/echarts.min.js"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #1a1a2e; color: white; }
        #map-container { width: 800px; height: 600px; background: #16213e; border: 1px solid #40e0d0; margin: 20px 0; }
        .info { background: #2a2a4e; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .step { margin: 10px 0; padding: 10px; background: #333366; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🗺️ ECharts 地图调试页面</h1>
    
    <div class="info">
        <h2>调试步骤</h2>
        <div id="debug-steps"></div>
    </div>

    <div id="map-container"></div>

    <div class="info">
        <h2>调试信息</h2>
        <div id="debug-info"></div>
    </div>

    <script>
        let debugSteps = [];
        let debugInfo = [];
        
        function addStep(message, type = 'info') {
            debugSteps.push(`<div class="step ${type}">${message}</div>`);
            document.getElementById('debug-steps').innerHTML = debugSteps.join('');
        }
        
        function addInfo(message, type = 'info') {
            debugInfo.push(`<div class="${type}">${message}</div>`);
            document.getElementById('debug-info').innerHTML = debugInfo.join('');
        }
        
        async function debugMap() {
            addStep('🚀 开始地图调试...', 'info');
            
            try {
                // 步骤1: 检查ECharts版本
                addStep(`📦 ECharts版本: ${echarts.version}`, 'success');
                
                // 步骤2: 加载地图数据
                addStep('📥 正在加载中国地图数据...', 'info');
                const response = await fetch('/maps/china.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const mapData = await response.json();
                addStep(`✅ 地图数据加载成功 (${mapData.features.length} 个省份)`, 'success');
                addInfo(`数据类型: ${mapData.type}`, 'info');
                addInfo(`特征数量: ${mapData.features.length}`, 'info');
                
                // 步骤3: 注册地图
                addStep('🔧 正在注册地图到ECharts...', 'info');
                echarts.registerMap('china', mapData);
                addStep('✅ 地图注册成功', 'success');
                
                // 步骤4: 创建图表实例
                addStep('🎨 正在创建图表实例...', 'info');
                const chartDom = document.getElementById('map-container');
                const myChart = echarts.init(chartDom);
                addStep('✅ 图表实例创建成功', 'success');
                
                // 步骤5: 配置地图选项
                addStep('⚙️ 正在配置地图选项...', 'info');
                const option = {
                    title: {
                        text: '中国地图调试',
                        left: 'center',
                        textStyle: {
                            color: '#ffffff',
                            fontSize: 20
                        }
                    },
                    geo: {
                        map: 'china',
                        roam: true,
                        itemStyle: {
                            areaColor: '#1e3c72',
                            borderColor: '#40e0d0',
                            borderWidth: 2
                        },
                        emphasis: {
                            itemStyle: {
                                areaColor: '#FFD700'
                            }
                        },
                        label: {
                            show: true,
                            color: '#ffffff',
                            fontSize: 12
                        }
                    }
                };
                
                // 步骤6: 应用配置
                addStep('🖼️ 正在渲染地图...', 'info');
                myChart.setOption(option);
                addStep('🎉 地图渲染完成！', 'success');
                
                // 添加交互事件
                myChart.on('click', function (params) {
                    if (params.componentType === 'geo') {
                        addInfo(`🖱️ 点击了: ${params.name}`, 'warning');
                    }
                });
                
                // 窗口大小调整
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
                
                addInfo('🎯 地图调试完成，应该能看到中国地图', 'success');
                addInfo('🖱️ 可以点击省份查看交互效果', 'info');
                addInfo('🔍 可以使用鼠标滚轮缩放地图', 'info');
                
            } catch (error) {
                addStep(`❌ 调试失败: ${error.message}`, 'error');
                addInfo(`错误详情: ${error.stack}`, 'error');
                console.error('地图调试错误:', error);
            }
        }
        
        // 页面加载完成后开始调试
        window.onload = function() {
            debugMap();
        };
    </script>
</body>
</html>
