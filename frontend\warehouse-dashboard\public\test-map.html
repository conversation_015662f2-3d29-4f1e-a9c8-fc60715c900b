<!DOCTYPE html>
<html>
<head>
    <title>地图数据测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div id="map" style="width: 800px; height: 600px;"></div>
    <div id="status"></div>
    
    <script>
        async function testMap() {
            const statusDiv = document.getElementById('status');
            
            try {
                statusDiv.innerHTML = '正在加载地图数据...';
                
                // 测试地图数据加载
                const response = await fetch('/maps/china.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const mapJson = await response.json();
                statusDiv.innerHTML = '地图数据加载成功，正在注册地图...';
                
                // 注册地图
                echarts.registerMap('china', mapJson);
                statusDiv.innerHTML = '地图注册成功，正在初始化图表...';
                
                // 初始化图表
                const chart = echarts.init(document.getElementById('map'));
                
                const option = {
                    title: {
                        text: '中国地图测试',
                        left: 'center',
                        textStyle: {
                            color: '#333'
                        }
                    },
                    geo: {
                        map: 'china',
                        roam: true,
                        itemStyle: {
                            areaColor: '#e0e0e0',
                            borderColor: '#404040',
                            borderWidth: 1
                        },
                        emphasis: {
                            itemStyle: {
                                areaColor: '#FFD700'
                            }
                        },
                        label: {
                            show: true,
                            color: '#000'
                        }
                    }
                };
                
                chart.setOption(option);
                statusDiv.innerHTML = '地图显示成功！';
                
            } catch (error) {
                statusDiv.innerHTML = `错误: ${error.message}`;
                console.error('地图测试失败:', error);
            }
        }
        
        // 页面加载后执行测试
        window.onload = testMap;
    </script>
</body>
</html>
