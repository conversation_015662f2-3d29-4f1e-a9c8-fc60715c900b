<template>
  <div class="dynamic-dashboard-test p-4 bg-slate-800 min-h-screen text-white">
    <h1 class="text-3xl font-bold mb-6">动态仪表盘测试</h1>
    
    <div class="mb-4">
      <button 
        @click="testLayoutStore" 
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2"
      >
        测试Layout Store
      </button>
      <button 
        @click="testGridStack" 
        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mr-2"
      >
        测试GridStack
      </button>
      <button 
        @click="clearLocalStorage" 
        class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
      >
        清除LocalStorage
      </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Layout Store 状态 -->
      <div class="bg-slate-700 rounded-lg p-4">
        <h2 class="text-xl font-semibold mb-3">Layout Store 状态</h2>
        <pre class="text-sm bg-slate-800 p-3 rounded overflow-auto">{{ JSON.stringify(layoutStore.layout, null, 2) }}</pre>
      </div>

      <!-- GridStack 测试区域 -->
      <div class="bg-slate-700 rounded-lg p-4">
        <h2 class="text-xl font-semibold mb-3">GridStack 测试</h2>
        <div ref="testGridRef" class="grid-stack bg-slate-600 rounded" style="min-height: 300px;">
          <!-- GridStack items will be added here -->
        </div>
      </div>
    </div>

    <!-- 组件测试区域 -->
    <div class="mt-6 bg-slate-700 rounded-lg p-4">
      <h2 class="text-xl font-semibold mb-3">组件测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <BasicInfoCard title="测试基本信息卡片" />
        <SalesChartCard title="测试销售图表卡片" />
        <KeyMetricCard title="测试关键指标卡片" />
      </div>
    </div>

    <!-- 错误日志 -->
    <div class="mt-6 bg-slate-700 rounded-lg p-4" v-if="errors.length > 0">
      <h2 class="text-xl font-semibold mb-3 text-red-400">错误日志</h2>
      <div v-for="(error, index) in errors" :key="index" class="text-red-300 text-sm mb-2">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'

import { useLayoutStore } from '@/stores/layout'
import BasicInfoCard from '@/components/cards/BasicInfoCard.vue'
import SalesChartCard from '@/components/cards/SalesChartCard.vue'
import KeyMetricCard from '@/components/cards/KeyMetricCard.vue'

const layoutStore = useLayoutStore()
const testGridRef = ref<HTMLElement | null>(null)
const errors = ref<string[]>([])
let testGrid: GridStack | null = null

const addError = (message: string) => {
  errors.value.push(`[${new Date().toLocaleTimeString()}] ${message}`)
  console.error(message)
}

const testLayoutStore = () => {
  try {
    console.log('Testing Layout Store...')
    console.log('Current layout:', layoutStore.layout)
    console.log('Layout length:', layoutStore.layout.length)
    
    // 测试添加一个新项目
    layoutStore.addLayoutItem({
      id: 'test-' + Date.now(),
      x: 0,
      y: 0,
      w: 2,
      h: 2,
      component: 'BasicInfoCard',
      props: { title: '测试项目' }
    })
    
    console.log('Added test item, new layout:', layoutStore.layout)
  } catch (error) {
    addError(`Layout Store test failed: ${error}`)
  }
}

const testGridStack = () => {
  try {
    console.log('Testing GridStack...')
    
    if (!testGridRef.value) {
      addError('Test grid container not found')
      return
    }

    if (testGrid) {
      testGrid.destroy()
    }

    testGrid = GridStack.init({
      column: 6,
      cellHeight: 60,
      margin: 4
    }, testGridRef.value)

    console.log('GridStack initialized:', testGrid)

    // 添加一个测试项目
    const testItem = testGrid.addWidget({
      w: 2,
      h: 2,
      content: '<div class="bg-blue-600 text-white p-2 rounded">测试项目</div>'
    })

    console.log('Added test widget:', testItem)
  } catch (error) {
    addError(`GridStack test failed: ${error}`)
  }
}

const clearLocalStorage = () => {
  try {
    localStorage.removeItem('dashboardLayout')
    console.log('LocalStorage cleared')
    // 重置layout store
    layoutStore.resetLayout()
  } catch (error) {
    addError(`Clear localStorage failed: ${error}`)
  }
}

onMounted(() => {
  console.log('DynamicDashboardTest mounted')
  errors.value = []
})

onUnmounted(() => {
  if (testGrid) {
    testGrid.destroy()
  }
})
</script>

<style scoped>
.dynamic-dashboard-test {
  font-family: 'Inter', sans-serif;
}

pre {
  max-height: 200px;
  overflow-y: auto;
}
</style>
