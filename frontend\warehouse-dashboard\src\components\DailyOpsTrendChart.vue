<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content" :id="chartId" ref="chartContainer"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '核心运营趋势'
  },
  chartId: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  }
})

const chartInstance = ref(null)
const chartContainer = ref(null)

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance.value = echarts.init(chartContainer.value)
  updateChart()
  window.addEventListener('resize', handleResize)
}

const updateChart = () => {
  if (!chartInstance.value) return
  
  const chartData = props.data.length > 0 ? processData() : getDefaultData()
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1E90FF',
      textStyle: { color: '#ffffff' }
    },
    legend: {
      data: ['TOC单量', 'TOB单量', '入库方数'],
      textStyle: { color: '#ffffff' },
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLine: { lineStyle: { color: '#1E90FF' } },
      axisLabel: { color: '#7BDEFF' }
    },
    yAxis: [
      {
        type: 'value',
        name: '单量',
        axisLine: { lineStyle: { color: '#1E90FF' } },
        axisLabel: { color: '#7BDEFF' },
        splitLine: { lineStyle: { color: 'rgba(30, 144, 255, 0.2)' } }
      },
      {
        type: 'value',
        name: '方数',
        axisLine: { lineStyle: { color: '#00CED1' } },
        axisLabel: { color: '#7BDEFF' },
        splitLine: { show: false }
      }
    ],
    series: [
      {
        name: 'TOC单量',
        type: 'line',
        data: chartData.tocOrders,
        smooth: true,
        lineStyle: { color: '#1E90FF', width: 3 },
        itemStyle: { color: '#1E90FF' },
        areaStyle: { color: 'rgba(30, 144, 255, 0.1)' }
      },
      {
        name: 'TOB单量',
        type: 'bar',
        data: chartData.tobOrders,
        itemStyle: { color: '#00CED1' }
      },
      {
        name: '入库方数',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.inboundVolume,
        smooth: true,
        lineStyle: { color: '#32CD32', width: 2 },
        itemStyle: { color: '#32CD32' }
      }
    ]
  }
  
  chartInstance.value.setOption(option, true)
}

const processData = () => {
  const last30Days = props.data.slice(-30)
  return {
    dates: last30Days.map(item => item.date?.substring(5) || ''),
    tocOrders: last30Days.map(item => item.toc_orders || 0),
    tobOrders: last30Days.map(item => item.tob_orders || 0),
    inboundVolume: last30Days.map(item => item.inbound_volume || 0)
  }
}

const getDefaultData = () => {
  const dates = []
  const tocOrders = []
  const tobOrders = []
  const inboundVolume = []
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(`${date.getMonth() + 1}-${date.getDate()}`)
    
    tocOrders.push(Math.floor(Math.random() * 5000) + 8000)
    tobOrders.push(Math.floor(Math.random() * 3000) + 5000)
    inboundVolume.push(Math.floor(Math.random() * 200) + 300)
  }
  
  return { dates, tocOrders, tobOrders, inboundVolume }
}

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 15px;
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
