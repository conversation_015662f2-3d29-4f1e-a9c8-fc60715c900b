/* 科技感背景样式 - 用CSS创建动态科技背景 */
.tech-background {
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(50, 205, 50, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, #0a1628 0%, #1a2332 25%, #0f1419 50%, #1e2a3a 75%, #0a1628 100%);
  
  position: relative;
  overflow: hidden;
}

.tech-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    /* 网格线效果 */
    linear-gradient(rgba(0, 191, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 191, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

.tech-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    /* 光点效果 */
    radial-gradient(circle at 10% 20%, rgba(0, 191, 255, 0.3) 1px, transparent 2px),
    radial-gradient(circle at 90% 80%, rgba(138, 43, 226, 0.3) 1px, transparent 2px),
    radial-gradient(circle at 30% 70%, rgba(50, 205, 50, 0.3) 1px, transparent 2px),
    radial-gradient(circle at 70% 30%, rgba(255, 215, 0, 0.3) 1px, transparent 2px);
  background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px;
  animation: stars-twinkle 15s ease-in-out infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes stars-twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* 数据流动效果 */
.data-stream {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(to bottom, transparent, rgba(0, 191, 255, 0.8), transparent);
  animation: stream-flow 3s linear infinite;
}

.data-stream:nth-child(1) { left: 10%; animation-delay: 0s; }
.data-stream:nth-child(2) { left: 30%; animation-delay: 1s; }
.data-stream:nth-child(3) { left: 50%; animation-delay: 2s; }
.data-stream:nth-child(4) { left: 70%; animation-delay: 0.5s; }
.data-stream:nth-child(5) { left: 90%; animation-delay: 1.5s; }

@keyframes stream-flow {
  0% { 
    top: -100px; 
    opacity: 0; 
  }
  10% { 
    opacity: 1; 
  }
  90% { 
    opacity: 1; 
  }
  100% { 
    top: 100vh; 
    opacity: 0; 
  }
}
