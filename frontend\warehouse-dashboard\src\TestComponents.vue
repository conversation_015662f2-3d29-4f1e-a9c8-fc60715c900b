<template>
  <div class="test-page">
    <AppHeader title="组件测试页面" />
    
    <div class="test-container">
      <div class="status-bar">
        <div class="status-item">
          <span class="label">后端连接:</span>
          <span :class="['status', apiStatus]">{{ apiStatusText }}</span>
        </div>
        <div class="status-item">
          <span class="label">数据条数:</span>
          <span class="value">{{ apiData.length }}</span>
        </div>
      </div>

      <div class="components-grid">
        <!-- 核心指标卡片 -->
        <div class="test-section">
          <h3>核心指标卡片</h3>
          <OverallKpiCard 
            title="核心运营指标"
            :data="apiData"
          />
        </div>

        <!-- 运营趋势图表 -->
        <div class="test-section">
          <h3>运营趋势图表</h3>
          <DailyOpsTrendChart 
            title="运营趋势分析"
            chart-id="trend-test"
            :data="apiData"
          />
        </div>

        <!-- 仓储状态仪表盘 -->
        <div class="test-section">
          <h3>仓储状态仪表盘</h3>
          <WarehouseStatusGauge 
            title="仓储状态监控"
            chart-id="gauge-test"
            :data="{ utilization_rate: 78.5, remaining_capacity: '2.1万m³' }"
          />
        </div>

        <!-- 库存健康度面板 -->
        <div class="test-section">
          <h3>库存健康度面板</h3>
          <InventoryHealthPanel 
            title="库存健康度"
            :data="{ turnover_rate: 2.3, accuracy_rate: 96.8 }"
          />
        </div>

        <!-- 人力成本饼图 -->
        <div class="test-section">
          <h3>人力成本饼图</h3>
          <LaborCostPieChart 
            title="人力成本结构"
            chart-id="labor-test"
            :data="{ regular_hours: 6800, temp_hours: 3200 }"
          />
        </div>

        <!-- 月度绩效看板 -->
        <div class="test-section">
          <h3>月度绩效看板</h3>
          <MonthlyKpiScoreboard 
            title="月度绩效看板"
            :data="monthlyKpiData"
          />
        </div>

        <!-- 人效对比表格 -->
        <div class="test-section">
          <h3>人效对比表格</h3>
          <PersonnelEfficiencyTable 
            title="人效对比分析"
            :data="personnelData"
          />
        </div>

        <!-- 服务质量跟踪 -->
        <div class="test-section">
          <h3>服务质量跟踪</h3>
          <ServiceQualityTracker 
            title="服务质量跟踪"
            chart-id="service-test"
            :data="apiData"
          />
        </div>

        <!-- 商品分类饼图 -->
        <div class="test-section">
          <h3>商品分类饼图</h3>
          <PieChart 
            title="商品分类占比"
            chart-id="pie-test"
            :data="categoryData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import AppHeader from './components/AppHeader.vue'
import OverallKpiCard from './components/OverallKpiCard.vue'
import DailyOpsTrendChart from './components/DailyOpsTrendChart.vue'
import WarehouseStatusGauge from './components/WarehouseStatusGauge.vue'
import InventoryHealthPanel from './components/InventoryHealthPanel.vue'
import LaborCostPieChart from './components/LaborCostPieChart.vue'
import MonthlyKpiScoreboard from './components/MonthlyKpiScoreboard.vue'
import PersonnelEfficiencyTable from './components/PersonnelEfficiencyTable.vue'
import ServiceQualityTracker from './components/ServiceQualityTracker.vue'
import PieChart from './components/PieChart.vue'

// 响应式数据
const apiData = ref([])
const apiStatus = ref('loading')
const apiStatusText = ref('连接中...')

// 模拟数据
const monthlyKpiData = ref([
  { name: 'TOC人效', actual: 85, target: 90, unit: '单/人日' },
  { name: 'TOB人效', actual: 120, target: 110, unit: '单/人日' },
  { name: '库存准确率', actual: 96.8, target: 95, unit: '%' },
  { name: '及时交付率', actual: 92.5, target: 95, unit: '%' },
  { name: '成本控制', actual: 88, target: 85, unit: '万元' },
  { name: '客户满意度', actual: 4.6, target: 4.5, unit: '分' }
])

const personnelData = ref([
  {
    warehouse_name: '武汉仓',
    toc_efficiency: 85.2,
    toc_mom_change: 5.3,
    toc_yoy_change: 12.8,
    tob_efficiency: 120.5,
    tob_mom_change: -2.1,
    tob_yoy_change: 8.9
  },
  {
    warehouse_name: '深圳仓',
    toc_efficiency: 92.8,
    toc_mom_change: 8.7,
    toc_yoy_change: 15.2,
    tob_efficiency: 135.2,
    tob_mom_change: 6.4,
    tob_yoy_change: 18.7
  }
])

const categoryData = ref([
  { value: 35, name: '电子产品' },
  { value: 25, name: '服装鞋帽' },
  { value: 20, name: '食品饮料' },
  { value: 12, name: '家居用品' },
  { value: 8, name: '其他' }
])

// 获取后端数据
const fetchData = async () => {
  try {
    apiStatus.value = 'loading'
    apiStatusText.value = '连接中...'
    
    const response = await axios.get('http://localhost:8000/api/v1/metrics/all')
    apiData.value = response.data
    
    apiStatus.value = 'success'
    apiStatusText.value = '连接成功'
    
    console.log('获取到数据:', response.data)
  } catch (error) {
    apiStatus.value = 'error'
    apiStatusText.value = '连接失败'
    console.error('获取数据失败:', error)
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
}

.status-bar {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 15px;
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  color: #7BDEFF;
  font-size: 0.9rem;
}

.status.loading {
  color: #FFD700;
}

.status.success {
  color: #00FF7F;
}

.status.error {
  color: #FF6B6B;
}

.value {
  color: #ffffff;
  font-weight: bold;
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-section {
  background: rgba(14, 38, 92, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  min-height: 400px;
}

.test-section h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .components-grid {
    grid-template-columns: 1fr;
  }
  
  .status-bar {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
