<!DOCTYPE html>
<html>
<head>
    <title>多维度筛选功能测试</title>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Aria<PERSON>, sans-serif;
            background: #1a1a2e; 
            color: white; 
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
            border-radius: 10px;
            border: 1px solid #40e0d0;
        }
        .header h1 {
            color: #7BDEFF;
            font-size: 2rem;
            margin: 0;
            text-shadow: 0 0 20px rgba(123, 222, 255, 0.6);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #40e0d0; 
            border-radius: 8px; 
            background: rgba(16, 33, 62, 0.8);
            backdrop-filter: blur(10px);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: rgba(10, 22, 52, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
        }
        .test-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #7BDEFF;
            margin-bottom: 12px;
            text-shadow: 0 0 10px rgba(123, 222, 255, 0.5);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #00FF88; box-shadow: 0 0 8px rgba(0, 255, 136, 0.6); }
        .status-warning { background: #FFD700; box-shadow: 0 0 8px rgba(255, 215, 0, 0.6); }
        .status-error { background: #FF6B6B; box-shadow: 0 0 8px rgba(255, 107, 107, 0.6); }
        .status-info { background: #00BFFF; box-shadow: 0 0 8px rgba(0, 191, 255, 0.6); }
        
        .test-item {
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            border-left: 3px solid transparent;
        }
        .test-item.success { border-left-color: #00FF88; }
        .test-item.warning { border-left-color: #FFD700; }
        .test-item.error { border-left-color: #FF6B6B; }
        .test-item.info { border-left-color: #00BFFF; }
        
        .quick-link {
            display: inline-block;
            padding: 8px 16px;
            background: rgba(0, 150, 255, 0.2);
            border: 1px solid rgba(0, 150, 255, 0.4);
            border-radius: 6px;
            color: #7BDEFF;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            margin: 5px;
        }
        .quick-link:hover {
            background: rgba(0, 150, 255, 0.3);
            border-color: rgba(0, 150, 255, 0.6);
            color: #FFFFFF;
        }
        
        .instructions {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #FFD700;
            margin-top: 0;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 1.5rem; }
            .test-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 多维度筛选功能测试</h1>
        <div style="margin-top: 15px; color: #B3E5FC; font-size: 0.9rem;">
            测试时间: <span id="test-time"></span> | 测试环境: http://localhost:5175/
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 快速访问</h2>
        <a href="http://localhost:5175/" class="quick-link" target="_blank">🏠 主应用 (新功能)</a>
        <a href="http://localhost:5175/test-cross-filter.html" class="quick-link" target="_blank">🔄 原交叉筛选测试</a>
        <a href="http://localhost:5175/comprehensive-test-report.html" class="quick-link" target="_blank">📊 综合测试报告</a>
    </div>

    <div class="instructions">
        <h3>🧪 多维度筛选测试指南</h3>
        <p><strong>新功能概述</strong>: 系统现在支持同时进行省份筛选和产品类别筛选，两个维度可以独立操作，互不干扰。</p>
        
        <h4>📋 测试步骤</h4>
        <ol>
            <li><strong>访问主应用</strong>: 点击上方"主应用"链接</li>
            <li><strong>观察全局筛选器</strong>: 页面顶部应显示新的全局筛选器组件</li>
            <li><strong>测试产品类别筛选</strong>:
                <ul>
                    <li>在全局筛选器中选择"电子产品"</li>
                    <li>观察"按类别销售额"图表数据变化</li>
                    <li>切换到"图书音像"，再观察数据变化</li>
                    <li>选择"家居用品"，验证数据正确切换</li>
                </ul>
            </li>
            <li><strong>测试省份筛选</strong>:
                <ul>
                    <li>点击地图上的"广东"省份</li>
                    <li>观察"月度销售趋势"图表数据变化</li>
                    <li>全局筛选器应显示当前选中的省份</li>
                </ul>
            </li>
            <li><strong>测试多维度组合筛选</strong>:
                <ul>
                    <li>同时选择省份(如广东)和产品类别(如电子产品)</li>
                    <li>验证两个筛选条件同时生效</li>
                    <li>观察筛选状态在全局筛选器中的显示</li>
                </ul>
            </li>
            <li><strong>测试筛选清除</strong>:
                <ul>
                    <li>使用全局筛选器中的"×"按钮清除单个筛选</li>
                    <li>使用"清除所有筛选"按钮清除全部筛选</li>
                    <li>验证图表数据恢复到默认状态</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🎯 预期功能验证</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">🔍 全局筛选器组件</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    页面顶部显示全局筛选器
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    包含产品类别下拉选择器
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    显示当前省份筛选状态
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    显示筛选摘要信息
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">📊 按类别销售额图表</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    新增图表显示在布局中
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    默认显示"全部"类别数据
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    响应产品类别筛选变化
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    数据切换流畅无延迟
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">🗺️ 省份筛选功能</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    地图点击筛选正常工作
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    月度销售趋势图响应筛选
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    筛选状态在全局筛选器显示
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    可通过全局筛选器清除
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">🔄 多维度组合筛选</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    省份和类别筛选独立工作
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    两个维度可同时生效
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    筛选状态正确维护
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    清除操作精确控制
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">💾 数据源管理</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    新增salesByCategory数据源
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    支持多维度筛选参数
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    向后兼容原有筛选逻辑
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    数据获取性能良好
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">🎨 用户界面体验</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    全局筛选器设计美观
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    响应式设计适配良好
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    操作反馈及时清晰
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    与原有界面风格统一
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📈 测试数据参考</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">📊 按类别销售数据</div>
                <p><strong>全部类别</strong>: 1月1500, 2月1700, 3月1600, 4月2100, 5月2300, 6月2200</p>
                <p><strong>电子产品</strong>: 1月700, 2月800, 3月750, 4月900, 5月1100, 6月1000</p>
                <p><strong>图书音像</strong>: 1月400, 2月500, 3月450, 4月600, 5月650, 6月650</p>
                <p><strong>家居用品</strong>: 1月400, 2月400, 3月400, 4月600, 5月550, 6月550</p>
            </div>
            
            <div class="test-card">
                <div class="test-title">🗺️ 省份销售数据</div>
                <p><strong>全国数据</strong>: 1月2100, 2月1800, 3月2500, 4月3200, 5月3000, 6月2800</p>
                <p><strong>广东数据</strong>: 1月350, 2月300, 3月400, 4月550, 5月500, 6月450</p>
                <p><strong>浙江数据</strong>: 1月250, 2月220, 3月310, 4月400, 5月380, 6月350</p>
                <p><strong>江苏数据</strong>: 1月280, 2月240, 3月330, 4月420, 5月400, 6月370</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎉 功能升级亮点</h2>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>多维度支持</strong>: 从单一省份筛选升级为支持省份+产品类别的多维度筛选
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>独立控制</strong>: 各个筛选维度可以独立设置和清除，互不干扰
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>全局管理</strong>: 统一的全局筛选器组件，提供集中的筛选状态管理
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>向后兼容</strong>: 保持原有地图筛选功能完全兼容，无破坏性变更
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>扩展性强</strong>: 架构设计支持未来添加更多筛选维度
        </div>
    </div>

    <script>
        // 设置测试时间
        document.getElementById('test-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 添加页面加载完成提示
        window.onload = function() {
            console.log('🔄 多维度筛选功能测试页面加载完成');
            console.log('📋 请按照测试指南逐步验证新功能');
            console.log('🎯 重点测试: 全局筛选器、按类别销售图表、多维度组合筛选');
        };
    </script>
</body>
</html>
