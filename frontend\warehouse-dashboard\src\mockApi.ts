/**
 * 模拟API服务
 * 使用TypeScript模拟后端API接口
 * 每个函数返回Promise并模拟网络延迟和错误
 */

import type {
  RegionGroupedKeyMetrics,
  RegionGroupedMonthlyOrders,
  RegionGroupedLogisticsData,
  RealTimeOrders,
  RealTimeUpdateEvent,
  SalesDistribution
} from './types/index'

// 工具函数：模拟网络请求（生产版本 - 移除延迟和错误模拟）
const simulateApiCall = <T>(dataGenerator: () => T): Promise<T> => {
  return new Promise((resolve, reject) => {
    try {
      // 生产环境：直接返回数据，无延迟和错误模拟
      const data = dataGenerator()
      console.log('✅ API调用成功')
      resolve(data)
    } catch (error: any) {
      console.error('❌ 数据生成失败:', error)
      reject(new Error('数据生成失败: ' + error.message))
    }
  })
}

// API接口1: 获取关键指标数据
export const fetchKeyMetrics = async (): Promise<RegionGroupedKeyMetrics> => {
  // 生产环境：移除延迟模拟
  // await sleep(1000)

  return simulateApiCall(() => {
    // 生成基础数据模板
    const generateMetrics = (multiplier = 1) => [
      {
        title: '今日订单',
        value: Math.floor((Math.random() * 500 + 1200) * multiplier),
        unit: '单',
        trend: (Math.random() - 0.5) * 20,
        icon: '📦'
      },
      {
        title: '运输中',
        value: Math.floor((Math.random() * 200 + 300) * multiplier),
        unit: '单',
        trend: (Math.random() - 0.5) * 15,
        icon: '🚛'
      },
      {
        title: '已完成',
        value: Math.floor((Math.random() * 800 + 2000) * multiplier),
        unit: '单',
        trend: (Math.random() - 0.5) * 10,
        icon: '✅'
      },
      {
        title: '库存周转',
        value: Math.floor((Math.random() * 5 + 12) * multiplier),
        unit: '天',
        trend: (Math.random() - 0.5) * 8,
        icon: '📊'
      },
      {
        title: '配送效率',
        value: Math.floor((Math.random() * 10 + 85) * multiplier),
        unit: '%',
        trend: (Math.random() - 0.5) * 5,
        icon: '⚡'
      },
      {
        title: '客户满意度',
        value: Math.floor((Math.random() * 5 + 92) * multiplier),
        unit: '%',
        trend: (Math.random() - 0.5) * 3,
        icon: '😊'
      }
    ];

    // 按区域生成数据
    const regions = {
      '华东区': generateMetrics(0.35),
      '华南区': generateMetrics(0.25),
      '华北区': generateMetrics(0.20),
      '华中区': generateMetrics(0.15),
      '西南区': generateMetrics(0.05)
    };

    // 计算总计数据
    const totalMetrics = generateMetrics(1).map((metric, index) => ({
      ...metric,
      value: Object.values(regions).reduce((sum, regionData) =>
        sum + regionData[index].value, 0
      )
    }));

    return {
      ...regions,
      '总计': totalMetrics
    };
  });
};

// API接口2: 获取销售分布数据（人力成本数据）
export const fetchSalesDistribution = async (): Promise<SalesDistribution> => {
  // 生产环境：移除延迟模拟
  // await sleep(800)

  return simulateApiCall(() => [
    { 
      name: '正式工成本', 
      value: Math.floor(Math.random() * 10000) + 50000 
    },
    { 
      name: '劳务工成本', 
      value: Math.floor(Math.random() * 8000) + 28000 
    },
    { 
      name: '临时工成本', 
      value: Math.floor(Math.random() * 5000) + 15000 
    },
    { 
      name: '加班费用', 
      value: Math.floor(Math.random() * 3000) + 10000 
    },
    { 
      name: '福利支出', 
      value: Math.floor(Math.random() * 2000) + 6000 
    }
  ]);
};

// API接口3: 获取月度订单数据
export const fetchMonthlyOrders = async (): Promise<RegionGroupedMonthlyOrders> => {
  // 生产环境：移除延迟模拟
  // await sleep(1200)

  return simulateApiCall(() => {
    // 生成过去12个月的日期
    const generateDateRange = () => {
      const dates = [];
      const now = new Date();
      for (let i = 11; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        dates.push({
          label: `${date.getMonth() + 1}月`,
          date: date.toISOString().split('T')[0] // YYYY-MM-DD 格式
        });
      }
      return dates;
    };

    const dateRange = generateDateRange();
    const baseValues = [820, 932, 901, 934, 1290, 1330, 1150, 980, 1080, 1200, 1350, 1420];

    // 生成区域数据
    const generateRegionData = (multiplier: number) => ({
      xAxisData: dateRange.map(d => d.label),
      seriesData: baseValues.map(value =>
        Math.floor(value * multiplier * (0.8 + Math.random() * 0.4))
      ),
      dates: dateRange.map(d => d.date) // 添加日期数组
    });

    const regions = {
      '华东区': generateRegionData(0.35),
      '华南区': generateRegionData(0.25),
      '华北区': generateRegionData(0.20),
      '华中区': generateRegionData(0.15),
      '西南区': generateRegionData(0.05)
    };

    // 计算总计数据
    const totalData = {
      xAxisData: dateRange.map(d => d.label),
      seriesData: dateRange.map((_, index) =>
        Object.values(regions).reduce((sum, regionData) =>
          sum + regionData.seriesData[index], 0
        )
      ),
      dates: dateRange.map(d => d.date)
    };

    return {
      ...regions,
      '总计': totalData
    };
  });
};

// API接口4: 获取物流数据（仓库和路线数据）
export const fetchLogisticsData = async (): Promise<RegionGroupedLogisticsData> => {
  // 生产环境：移除延迟模拟
  // await sleep(1500)

  return simulateApiCall(() => {
    // 生成过去30天的日期范围
    const generateDateRange = () => {
      const dates = [];
      const now = new Date();
      for (let i = 29; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().split('T')[0]); // YYYY-MM-DD 格式
      }
      return dates;
    };

    const dateRange = generateDateRange();

    // 定义区域仓库映射（为每个仓库添加历史数据）
    const generateWarehouseData = (name: string, coords: [number, number], baseValue: number) => {
      return dateRange.map(date => ({
        name,
        value: [coords[0], coords[1], Math.floor(baseValue * (0.8 + Math.random() * 0.4))],
        date
      }));
    };

    const regionWarehouses = {
      '华东区': [
        ...generateWarehouseData('上海', [121.47, 31.23], 150),
        ...generateWarehouseData('杭州', [120.19, 30.26], 90),
        ...generateWarehouseData('南京', [118.78, 32.04], 80)
      ],
      '华南区': [
        ...generateWarehouseData('广州', [113.23, 23.16], 130),
        ...generateWarehouseData('深圳', [114.07, 22.62], 170),
        ...generateWarehouseData('厦门', [118.11, 24.49], 70)
      ],
      '华北区': [
        ...generateWarehouseData('北京', [116.40, 39.90], 100),
        ...generateWarehouseData('天津', [117.20, 39.13], 80),
        ...generateWarehouseData('青岛', [120.33, 36.07], 70)
      ],
      '华中区': [
        ...generateWarehouseData('武汉', [114.31, 30.52], 80),
        ...generateWarehouseData('长沙', [112.94, 28.23], 70)
      ],
      '西南区': [
        ...generateWarehouseData('成都', [104.06, 30.67], 110),
        ...generateWarehouseData('重庆', [106.54, 29.59], 80)
      ]
    };

    // 生成区域路线（为每条路线添加历史数据）
    const generateRegionRoutes = (warehouses: any[]) => {
      const routes: any[] = [];
      const uniqueWarehouses = [...new Set(warehouses.map(w => w.name))];

      for (let i = 0; i < uniqueWarehouses.length - 1; i++) {
        const fromWarehouse = warehouses.find(w => w.name === uniqueWarehouses[i]);
        const toWarehouse = warehouses.find(w => w.name === uniqueWarehouses[i + 1]);

        dateRange.forEach(date => {
          routes.push({
            fromName: fromWarehouse.name,
            toName: toWarehouse.name,
            coords: [
              [fromWarehouse.value[0], fromWarehouse.value[1]],
              [toWarehouse.value[0], toWarehouse.value[1]]
            ],
            date,
            volume: Math.floor(Math.random() * 50) + 20 // 运输量
          });
        });
      }
      return routes;
    };

    // 按区域生成数据
    const regions: any = {};
    Object.keys(regionWarehouses).forEach(region => {
      regions[region] = {
        warehouseData: regionWarehouses[region as keyof typeof regionWarehouses],
        routeData: generateRegionRoutes(regionWarehouses[region as keyof typeof regionWarehouses])
      };
    });

    // 生成总计数据（所有仓库和路线）
    const allWarehouses = Object.values(regionWarehouses).flat();
    const allRoutes = [
      ...dateRange.map(date => ({
        fromName: '北京', toName: '上海',
        coords: [[116.40, 39.90], [121.47, 31.23]],
        date, volume: Math.floor(Math.random() * 100) + 50
      })),
      ...dateRange.map(date => ({
        fromName: '上海', toName: '广州',
        coords: [[121.47, 31.23], [113.23, 23.16]],
        date, volume: Math.floor(Math.random() * 80) + 40
      })),
      ...dateRange.map(date => ({
        fromName: '广州', toName: '深圳',
        coords: [[113.23, 23.16], [114.07, 22.62]],
        date, volume: Math.floor(Math.random() * 60) + 30
      }))
    ];

    return {
      ...regions,
      '总计': {
        warehouseData: allWarehouses,
        routeData: allRoutes
      }
    };
  });
};

// API接口5: 获取实时订单数据
export const fetchRealTimeOrders = async (): Promise<RealTimeOrders> => {
  // 生产环境：移除延迟模拟
  // await sleep(600)

  return simulateApiCall(() => {
    const origins = ['上海仓', '北京仓', '广州仓', '深圳仓', '武汉仓', '成都仓', '西安仓', '杭州仓', '南京仓', '天津仓'];
    const destinations = ['北京分拨中心', '上海配送站', '广州配送站', '深圳配送站', '武汉配送站', '成都配送站', '西安配送站', '杭州配送站'];
    const statuses: ('已发货' | '运输中' | '配送中' | '已送达' | '异常')[] = ['已发货', '运输中', '配送中', '已送达', '异常'];
    
    const orders = [];
    const orderCount = Math.floor(Math.random() * 8) + 12; // 12-20个订单
    
    for (let i = 0; i < orderCount; i++) {
      const timestamp = Date.now() - (i * 1000 * 60 * Math.random() * 30); // 最近30分钟内的订单
      const dateStr = new Date(timestamp).toISOString().slice(2, 10).replace(/-/g, '');
      
      orders.push({
        orderId: `${origins[Math.floor(Math.random() * origins.length)].slice(0, 2)}${dateStr}${String(i + 1).padStart(3, '0')}`,
        origin: origins[Math.floor(Math.random() * origins.length)],
        destination: destinations[Math.floor(Math.random() * destinations.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)]
      });
    }
    
    return orders;
  });
};

// 导出所有API函数
export default {
  fetchKeyMetrics,
  fetchSalesDistribution,
  fetchMonthlyOrders,
  fetchLogisticsData,
  fetchRealTimeOrders
};

// 工具函数：批量获取所有数据
export const fetchAllDashboardData = async (): Promise<{
  keyMetrics: RegionGroupedKeyMetrics
  salesDistribution: SalesDistribution
  monthlyOrders: RegionGroupedMonthlyOrders
  logisticsData: RegionGroupedLogisticsData
  realTimeOrders: RealTimeOrders
}> => {
  try {
    const [
      keyMetrics,
      salesDistribution,
      monthlyOrders,
      logisticsData,
      realTimeOrders
    ] = await Promise.all([
      fetchKeyMetrics(),
      fetchSalesDistribution(),
      fetchMonthlyOrders(),
      fetchLogisticsData(),
      fetchRealTimeOrders()
    ])

    return {
      keyMetrics,
      salesDistribution,
      monthlyOrders,
      logisticsData,
      realTimeOrders
    }
  } catch (error: any) {
    throw new Error(`获取仪表盘数据失败: ${error.message}`)
  }
}

// 模拟数据刷新API
export const refreshDashboardData = (): Promise<{
  keyMetrics: RegionGroupedKeyMetrics
  salesDistribution: SalesDistribution
  monthlyOrders: RegionGroupedMonthlyOrders
  logisticsData: RegionGroupedLogisticsData
  realTimeOrders: RealTimeOrders
}> => {
  console.log('🔄 刷新仪表盘数据...')
  return fetchAllDashboardData()
}

// 模拟实时数据推送（WebSocket模拟）
export const subscribeToRealTimeUpdates = (callback: (update: RealTimeUpdateEvent) => void): (() => void) => {
  const interval = setInterval(async () => {
    try {
      const realTimeOrders = await fetchRealTimeOrders()
      callback({
        type: 'orders',
        data: realTimeOrders,
        timestamp: Date.now()
      })
    } catch (error: any) {
      console.error('实时数据更新失败:', error)
    }
  }, 30000) // 每30秒更新一次

  return () => clearInterval(interval)
}
