<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content" :id="chartId" ref="chartContainer"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '服务质量跟踪'
  },
  chartId: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  }
})

const chartInstance = ref(null)
const chartContainer = ref(null)

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance.value = echarts.init(chartContainer.value)
  updateChart()
  window.addEventListener('resize', handleResize)
}

const updateChart = () => {
  if (!chartInstance.value) return
  
  const chartData = props.data.length > 0 ? processData() : getDefaultData()
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1E90FF',
      textStyle: { color: '#ffffff' },
      formatter: function(params) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          const unit = param.seriesName === '客户投诉数量' ? '件' : '单'
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['客户投诉数量', '逆向物流单量'],
      textStyle: { color: '#ffffff' },
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLine: { lineStyle: { color: '#1E90FF' } },
      axisLabel: { 
        color: '#7BDEFF',
        rotate: 45
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '投诉数量(件)',
        position: 'left',
        axisLine: { lineStyle: { color: '#FF6B6B' } },
        axisLabel: { color: '#7BDEFF' },
        splitLine: { lineStyle: { color: 'rgba(255, 107, 107, 0.2)' } }
      },
      {
        type: 'value',
        name: '逆向单量(单)',
        position: 'right',
        axisLine: { lineStyle: { color: '#FFD700' } },
        axisLabel: { color: '#7BDEFF' },
        splitLine: { show: false }
      }
    ],
    series: [
      {
        name: '客户投诉数量',
        type: 'line',
        yAxisIndex: 0,
        data: chartData.complaints,
        smooth: true,
        lineStyle: { 
          color: '#FF6B6B', 
          width: 3,
          shadowColor: 'rgba(255, 107, 107, 0.3)',
          shadowBlur: 10
        },
        itemStyle: { 
          color: '#FF6B6B',
          borderWidth: 2,
          borderColor: '#ffffff'
        },
        areaStyle: { 
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
              { offset: 1, color: 'rgba(255, 107, 107, 0.05)' }
            ]
          }
        },
        markLine: {
          data: [
            {
              type: 'average',
              name: '平均值',
              lineStyle: { color: '#FF6B6B', type: 'dashed' }
            }
          ]
        }
      },
      {
        name: '逆向物流单量',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.reverseOrders,
        smooth: true,
        lineStyle: { 
          color: '#FFD700', 
          width: 3,
          shadowColor: 'rgba(255, 215, 0, 0.3)',
          shadowBlur: 10
        },
        itemStyle: { 
          color: '#FFD700',
          borderWidth: 2,
          borderColor: '#ffffff'
        },
        areaStyle: { 
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 215, 0, 0.3)' },
              { offset: 1, color: 'rgba(255, 215, 0, 0.05)' }
            ]
          }
        },
        markLine: {
          data: [
            {
              type: 'average',
              name: '平均值',
              lineStyle: { color: '#FFD700', type: 'dashed' }
            }
          ]
        }
      }
    ]
  }
  
  chartInstance.value.setOption(option, true)
}

const processData = () => {
  const last30Days = props.data.slice(-30)
  return {
    dates: last30Days.map(item => {
      const date = new Date(item.date)
      return `${date.getMonth() + 1}-${date.getDate()}`
    }),
    complaints: last30Days.map(item => item.customer_complaints || 0),
    reverseOrders: last30Days.map(item => item.reverse_logistics_orders || 0)
  }
}

const getDefaultData = () => {
  const dates = []
  const complaints = []
  const reverseOrders = []
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(`${date.getMonth() + 1}-${date.getDate()}`)
    
    // 模拟投诉数量（一般较低）
    complaints.push(Math.floor(Math.random() * 15) + 5)
    
    // 模拟逆向物流单量
    reverseOrders.push(Math.floor(Math.random() * 100) + 50)
  }
  
  return { dates, complaints, reverseOrders }
}

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 15px;
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
