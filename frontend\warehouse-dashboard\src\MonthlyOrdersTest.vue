<template>
  <div class="monthly-orders-test">
    <AppHeader title="月度订单趋势图组件测试" />
    
    <div class="test-container">
      <div class="controls-section">
        <h2>控制面板</h2>
        <div class="controls-grid">
          <div class="control-group">
            <label>数据集选择:</label>
            <select v-model="selectedDataset" @change="switchDataset">
              <option value="orders">月度订单量</option>
              <option value="revenue">月度营收</option>
              <option value="efficiency">月度人效</option>
              <option value="cost">月度成本</option>
              <option value="empty">空数据测试</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>主题色:</label>
            <select v-model="selectedTheme">
              <option value="#00CED1">青色 (默认)</option>
              <option value="#FFD700">金色</option>
              <option value="#1E90FF">科技蓝</option>
              <option value="#00FF7F">春绿色</option>
              <option value="#FF6B6B">珊瑚红</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="showLabel" />
              显示数值标签
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="enableAnimation" />
              启用动画
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="isLoading" />
              加载状态
            </label>
          </div>
          
          <div class="control-group">
            <button @click="refreshData" class="refresh-btn">
              刷新数据
            </button>
          </div>
        </div>
      </div>

      <div class="charts-section">
        <h2>图表展示</h2>
        <div class="charts-grid">
          <!-- 标准尺寸图表 -->
          <div class="chart-wrapper">
            <h3>标准尺寸 (600x300)</h3>
            <MonthlyOrdersChart
              :chartData="currentData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :showLabel="showLabel"
              :enableAnimation="enableAnimation"
              :themeColor="selectedTheme"
              chartHeight="300px"
            />
          </div>
          
          <!-- 大尺寸图表 -->
          <div class="chart-wrapper large">
            <h3>大尺寸 (800x400)</h3>
            <MonthlyOrdersChart
              :chartData="currentData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :showLabel="showLabel"
              :enableAnimation="enableAnimation"
              :themeColor="selectedTheme"
              chartHeight="400px"
            />
          </div>
          
          <!-- 小尺寸图表 -->
          <div class="chart-wrapper small">
            <h3>小尺寸 (400x200)</h3>
            <MonthlyOrdersChart
              :chartData="currentData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :showLabel="false"
              :enableAnimation="enableAnimation"
              :themeColor="selectedTheme"
              chartHeight="200px"
            />
          </div>
          
          <!-- 实时数据图表 -->
          <div class="chart-wrapper">
            <h3>实时数据更新</h3>
            <MonthlyOrdersChart
              :chartData="realtimeData"
              title="实时订单趋势"
              subtitle="每5秒自动更新"
              :loading="false"
              :showLabel="true"
              :enableAnimation="true"
              themeColor="#00FF7F"
              chartHeight="300px"
            />
          </div>
        </div>
      </div>

      <div class="data-section">
        <h2>当前数据结构</h2>
        <div class="data-display">
          <div class="data-item">
            <h4>X轴数据 (xAxisData):</h4>
            <pre>{{ JSON.stringify(currentData.xAxisData, null, 2) }}</pre>
          </div>
          <div class="data-item">
            <h4>Y轴数据 (seriesData):</h4>
            <pre>{{ JSON.stringify(currentData.seriesData, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppHeader from './components/AppHeader.vue'
import MonthlyOrdersChart from './components/MonthlyOrdersChart.vue'

// 响应式数据
const selectedDataset = ref('orders')
const selectedTheme = ref('#00CED1')
const showLabel = ref(true)
const enableAnimation = ref(true)
const isLoading = ref(false)

// 预设数据集
const datasets = {
  orders: {
    title: '月度订单趋势',
    subtitle: '过去六个月订单量变化',
    data: {
      xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
      seriesData: [820, 932, 901, 934, 1290, 1330]
    }
  },
  revenue: {
    title: '月度营收趋势',
    subtitle: '过去六个月营收变化（万元）',
    data: {
      xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
      seriesData: [156, 189, 175, 198, 245, 268]
    }
  },
  efficiency: {
    title: '月度人效趋势',
    subtitle: '过去六个月人效变化（单/人日）',
    data: {
      xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
      seriesData: [85, 92, 88, 95, 102, 108]
    }
  },
  cost: {
    title: '月度成本趋势',
    subtitle: '过去六个月成本变化（万元）',
    data: {
      xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
      seriesData: [125, 138, 142, 135, 158, 162]
    }
  },
  empty: {
    title: '空数据测试',
    subtitle: '测试无数据状态',
    data: {
      xAxisData: [],
      seriesData: []
    }
  }
}

// 实时数据（模拟动态更新）
const realtimeData = ref({
  xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月'],
  seriesData: [820, 932, 901, 934, 1290, 1330]
})

// 当前数据
const currentData = computed(() => datasets[selectedDataset.value].data)
const currentTitle = computed(() => datasets[selectedDataset.value].title)
const currentSubtitle = computed(() => datasets[selectedDataset.value].subtitle)

// 切换数据集
const switchDataset = () => {
  console.log('切换到数据集:', selectedDataset.value)
}

// 刷新数据
const refreshData = () => {
  // 模拟数据刷新
  const currentDataset = datasets[selectedDataset.value]
  if (currentDataset.data.seriesData.length > 0) {
    currentDataset.data.seriesData = currentDataset.data.seriesData.map(value => {
      // 随机变化 ±20%
      const change = (Math.random() - 0.5) * 0.4
      return Math.round(value * (1 + change))
    })
  }
  
  console.log('数据已刷新')
}

// 实时数据更新
const updateRealtimeData = () => {
  realtimeData.value.seriesData = realtimeData.value.seriesData.map(value => {
    // 随机变化 ±10%
    const change = (Math.random() - 0.5) * 0.2
    return Math.round(value * (1 + change))
  })
}

let realtimeInterval = null

onMounted(() => {
  // 启动实时数据更新
  realtimeInterval = setInterval(updateRealtimeData, 5000)
  console.log('月度订单趋势图测试页面已加载')
})

onUnmounted(() => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
  }
})
</script>

<style scoped>
.monthly-orders-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
}

.controls-section,
.charts-section,
.data-section {
  margin-bottom: 40px;
}

.controls-section h2,
.charts-section h2,
.data-section h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  background: rgba(14, 38, 92, 0.6);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #1E90FF;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  color: #7BDEFF;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group select {
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid #1E90FF;
  color: #ffffff;
  padding: 8px;
  border-radius: 4px;
}

.control-group input[type="checkbox"] {
  accent-color: #1E90FF;
}

.refresh-btn {
  background: rgba(0, 255, 127, 0.8);
  color: #ffffff;
  border: 1px solid #00FF7F;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(0, 255, 127, 1);
  transform: translateY(-2px);
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
}

.chart-wrapper {
  background: rgba(14, 38, 92, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  min-height: 400px;
}

.chart-wrapper.large {
  grid-column: span 2;
  min-height: 500px;
}

.chart-wrapper.small {
  min-height: 300px;
}

.chart-wrapper h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1rem;
}

.data-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.data-item {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
}

.data-item h4 {
  color: #7BDEFF;
  margin-bottom: 10px;
  font-size: 1rem;
}

.data-item pre {
  color: #7BDEFF;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  margin: 0;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-wrapper.large {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .controls-grid {
    grid-template-columns: 1fr;
  }
  
  .data-display {
    grid-template-columns: 1fr;
  }
}
</style>
