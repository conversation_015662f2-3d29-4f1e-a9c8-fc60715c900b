<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { sciFiTheme } from '@/echarts-theme';
import { useDataSourceStore } from '@/stores/dataSource';
import { useFilterStore } from '@/stores/filterStore';
 
const props = defineProps({
  title: {
    type: String,
    default: '地图'
  },
  dataSourceId: {
    type: String,
    default: null
  },
  widgetId: {
    type: String,
    default: undefined
  }
});
 
const chartRef = ref(null);
const chartInstance = ref(null);
const dataSourceStore = useDataSourceStore();
const filterStore = useFilterStore(); // 2. 获取 filter store 实例
 
const initChart = async () => {
  try {
    // 1. 获取地图JSON并注册地图
    console.log('开始加载地图数据...');
    const response = await fetch('/maps/china.json');
    if (!response.ok) {
      throw new Error(`地图数据加载失败: ${response.status}`);
    }
    const mapJson = await response.json();
    echarts.registerMap('china', mapJson);
    console.log('地图数据加载成功');

    // 2. 注册主题并初始化
    echarts.registerTheme('sci-fi', sciFiTheme);
    chartInstance.value = echarts.init(chartRef.value, 'sci-fi');
    console.log('地图组件初始化成功');

    // 3. 监听图表点击事件
    chartInstance.value.on('click', (params) => {
      // params.name 就是被点击区域的名称，即"省份"
      const provinceName = params.name;

      if (provinceName) {
        // 如果用户点击了有效省份，并且不是当前已选中的省份
        if (filterStore.currentFilter.province !== provinceName) {
          console.log(`[MapCard] 点击省份: ${provinceName}`);
          filterStore.setFilter(provinceName);

          // 提供视觉反馈，高亮选中区域
          chartInstance.value.dispatchAction({
            type: 'downplay',
            seriesIndex: 0
          });
          chartInstance.value.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            name: provinceName
          });
        } else {
          // 如果用户重复点击已选中省份，清除筛选
          console.log(`[MapCard] 取消筛选: ${provinceName}`);
          filterStore.clearFilter();

          // 取消所有高亮
          chartInstance.value.dispatchAction({
            type: 'downplay',
            seriesIndex: 0
          });
        }
      } else {
        // 如果点击了空白区域（海洋、南海诸岛等），清除筛选
        if (filterStore.currentFilter.province) {
          console.log(`[MapCard] 点击空白区域，清除筛选`);
          filterStore.clearFilter();

          // 取消所有高亮
          chartInstance.value.dispatchAction({
            type: 'downplay',
            seriesIndex: 0
          });
        }
      }
    });

    // 4. 初始加载数据
    updateChartData();
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
};
 
const updateChartData = async () => {
  if (!props.dataSourceId || !chartInstance.value) {
    console.log('地图数据更新跳过: dataSourceId=', props.dataSourceId, 'chartInstance=', !!chartInstance.value);
    return;
  }

  try {
    chartInstance.value.showLoading();
    const data = await dataSourceStore.fetchData(props.dataSourceId);
    console.log('地图数据获取成功:', data);
    chartInstance.value.hideLoading();
 
  chartInstance.value.setOption({
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>{a}: {c}'
    },
    // 视觉映射组件，用于根据数据大小映射颜色
    visualMap: {
      min: 0,
      max: 5000, // 建议根据你的数据范围动态设置
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#002256', '#0055a4', '#0091ff', '#40e0d0'] // 从深蓝到青色的渐变
      },
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '业务量', // 这个会显示在 tooltip 中
        type: 'map',
        map: 'china',
        roam: true, // 开启鼠标缩放和平移漫游
        label: {
          show: false // 默认不显示省份标签
        },
        emphasis: {
          label: {
            show: true,
            color: '#fff'
          },
          itemStyle: {
            areaColor: '#FFD700' // 高亮时的颜色
          }
        },
        data: data
      }
    ]
  });
  } catch (error) {
    console.error('地图数据更新失败:', error);
    if (chartInstance.value) {
      chartInstance.value.hideLoading();
    }
  }
};
 
onMounted(() => {
  initChart();
});
 
watch(() => [props.dataSourceId, props.title], () => {
  updateChartData();
}, { deep: true });
</script>
 
<template>
  <div class="map-card h-full bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">🗺️</div>
    </div>
    <div ref="chartRef" class="w-full" style="height: calc(100% - 60px);"></div>
  </div>
</template>
