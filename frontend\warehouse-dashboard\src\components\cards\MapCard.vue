<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { sciFiTheme } from '@/echarts-theme';
import { useDataSourceStore } from '@/stores/dataSource';
import { useFilterStore } from '@/stores/filterStore';
 
const props = defineProps({
  title: {
    type: String,
    default: '地图'
  },
  dataSourceId: {
    type: String,
    default: null
  },
  widgetId: {
    type: String,
    default: undefined
  }
});
 
const chartRef = ref(null);
const chartInstance = ref(null);
const dataSourceStore = useDataSourceStore();
const filterStore = useFilterStore(); // 2. 获取 filter store 实例
 
const initChart = async () => {
  try {
    // 1. 获取地图JSON并注册地图
    console.log('开始加载地图数据...');
    const response = await fetch('/maps/china.json');
    if (!response.ok) {
      throw new Error(`地图数据加载失败: ${response.status}`);
    }
    const mapJson = await response.json();
    echarts.registerMap('china', mapJson);
    console.log('地图数据加载成功');

    // 2. 注册主题并初始化
    echarts.registerTheme('sci-fi', sciFiTheme);
    chartInstance.value = echarts.init(chartRef.value, 'sci-fi');
    console.log('地图组件初始化成功');

    // 3. 监听图表点击事件
    chartInstance.value.on('click', (params) => {
      // params.name 就是被点击区域的名称，即"省份"
      const provinceName = params.name;

      if (provinceName) {
        // 如果用户点击了有效省份，并且不是当前已选中的省份
        if (filterStore.currentFilter.province !== provinceName) {
          console.log(`[MapCard] 点击省份: ${provinceName}`);
          filterStore.setFilter(provinceName);

          // 提供视觉反馈，高亮选中区域
          chartInstance.value.dispatchAction({
            type: 'downplay',
            seriesIndex: 0
          });
          chartInstance.value.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            name: provinceName
          });
        } else {
          // 如果用户重复点击已选中省份，清除筛选
          console.log(`[MapCard] 取消筛选: ${provinceName}`);
          filterStore.clearFilter();

          // 取消所有高亮
          chartInstance.value.dispatchAction({
            type: 'downplay',
            seriesIndex: 0
          });
        }
      } else {
        // 如果点击了空白区域（海洋、南海诸岛等），清除筛选
        if (filterStore.currentFilter.province) {
          console.log(`[MapCard] 点击空白区域，清除筛选`);
          filterStore.clearFilter();

          // 取消所有高亮
          chartInstance.value.dispatchAction({
            type: 'downplay',
            seriesIndex: 0
          });
        }
      }
    });

    // 4. 初始加载数据
    updateChartData();
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
};
 
const updateChartData = async () => {
  if (!chartInstance.value) {
    console.log('地图数据更新跳过: chartInstance未初始化');
    return;
  }

  let data = [];

  try {
    if (props.dataSourceId) {
      chartInstance.value.showLoading();
      data = await dataSourceStore.fetchData(props.dataSourceId);
      console.log('地图数据获取成功:', data);
      chartInstance.value.hideLoading();
    } else {
      console.log('无数据源ID，显示基础地图');
    }
 
    // 配置地图选项
    const option = {
      title: {
        text: props.title,
        left: 'center',
        top: '10px',
        textStyle: {
          color: '#EFEFEF',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: data.length > 0 ? '{b}<br/>{a}: {c}' : '{b}',
        backgroundColor: 'rgba(10, 22, 52, 0.9)',
        borderColor: 'rgba(0, 150, 255, 0.6)',
        borderWidth: 1,
        textStyle: {
          color: '#EFEFEF'
        }
      },
      // 只有在有数据时才显示视觉映射
      visualMap: data.length > 0 ? {
        min: 0,
        max: Math.max(...data.map(item => item.value || 0)),
        text: ['高', '低'],
        realtime: false,
        calculable: true,
        inRange: {
          color: ['#002256', '#0055a4', '#0091ff', '#40e0d0']
        },
        textStyle: {
          color: '#fff'
        },
        left: 'left',
        bottom: '20px'
      } : undefined,
      series: [
        {
          name: '业务量',
          type: 'map',
          map: 'china',
          roam: true,
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              color: '#fff'
            },
            itemStyle: {
              areaColor: '#FFD700'
            }
          },
          itemStyle: {
            normal: {
              areaColor: data.length > 0 ? undefined : '#0055a4',
              borderColor: '#40e0d0',
              borderWidth: 1
            }
          },
          data: data
        }
      ]
    };

    chartInstance.value.setOption(option);
  } catch (error) {
    console.error('地图数据更新失败:', error);
    if (chartInstance.value) {
      chartInstance.value.hideLoading();
    }
  }
};
 
onMounted(async () => {
  await initChart();
  // 地图初始化完成后，加载数据
  updateChartData();
});

watch(() => [props.dataSourceId, props.title], () => {
  updateChartData();
}, { deep: true });
</script>
 
<template>
  <div class="map-card h-full bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">🗺️</div>
    </div>
    <div ref="chartRef" class="w-full" style="height: calc(100% - 60px);"></div>
  </div>
</template>
