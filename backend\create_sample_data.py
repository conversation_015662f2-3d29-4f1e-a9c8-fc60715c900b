#!/usr/bin/env python3
"""
创建示例Excel数据文件的脚本
"""
import pandas as pd
from datetime import datetime, timedelta

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 创建示例数据
    data = []
    
    # 生成最近7天的数据
    base_date = datetime.now() - timedelta(days=7)
    
    warehouses = [
        {"name": "武汉仓", "base_shipping": 150, "base_pending": 25, "base_efficiency": 12.5},
        {"name": "深圳仓", "base_shipping": 200, "base_pending": 18, "base_efficiency": 15.8},
        {"name": "黄冈仓", "base_shipping": 120, "base_pending": 30, "base_efficiency": 10.2},
        {"name": "天门仓", "base_shipping": 180, "base_pending": 22, "base_efficiency": 14.1}
    ]
    
    for i in range(7):  # 7天数据
        current_date = base_date + timedelta(days=i)
        date_str = current_date.strftime("%Y-%m-%d")
        
        for warehouse in warehouses:
            # 添加一些随机变化
            import random
            shipping_variation = random.randint(-20, 30)
            pending_variation = random.randint(-5, 10)
            efficiency_variation = random.uniform(-2.0, 3.0)
            
            data.append({
                "date": date_str,
                "warehouse_name": warehouse["name"],
                "shipping_orders": max(0, warehouse["base_shipping"] + shipping_variation),
                "pending_orders": max(0, warehouse["base_pending"] + pending_variation),
                "efficiency_orders_per_hour": round(max(0, warehouse["base_efficiency"] + efficiency_variation), 2)
            })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    df.to_excel("data.xlsx", index=False)
    print("示例Excel文件 'data.xlsx' 已创建成功！")
    print(f"包含 {len(data)} 条记录")
    print("\n文件内容预览:")
    print(df.head(10))

if __name__ == "__main__":
    create_sample_excel()
