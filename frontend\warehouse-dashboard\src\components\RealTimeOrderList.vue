<template>
  <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200 transition-all duration-300 hover:shadow-lg">
    <!-- 组件标题 -->
    <div class="list-header mb-4">
      <h3 class="text-lg font-semibold text-slate-800 mb-2">{{ title }}</h3>
      <div class="text-sm text-slate-600" v-if="subtitle">{{ subtitle }}</div>
    </div>
    
    <!-- 表格容器 -->
    <div class="table-container">
      <!-- 固定表头 -->
      <div class="table-header">
        <div class="header-cell order-id">订单号</div>
        <div class="header-cell origin">始发地</div>
        <div class="header-cell destination">目的地</div>
        <div class="header-cell status">状态</div>
      </div>
      
      <!-- 滚动内容区域 -->
      <div class="scroll-container" :class="{ 'paused': isPaused }">
        <div class="scroll-content" :style="scrollStyle">
          <!-- 第一份数据 -->
          <div class="data-group">
            <div 
              v-for="(order, index) in displayData" 
              :key="`first-${order.orderId}-${index}`"
              class="table-row"
              :class="{ 'even': index % 2 === 0, 'odd': index % 2 === 1 }"
            >
              <div class="table-cell order-id">
                <span class="order-number">{{ order.orderId }}</span>
              </div>
              <div class="table-cell origin">
                <span class="location-tag origin-tag">{{ order.origin }}</span>
              </div>
              <div class="table-cell destination">
                <span class="location-tag destination-tag">{{ order.destination }}</span>
              </div>
              <div class="table-cell status">
                <span class="status-badge" :class="getStatusClass(order.status)">
                  {{ order.status }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 第二份数据（用于无缝滚动） -->
          <div class="data-group">
            <div 
              v-for="(order, index) in displayData" 
              :key="`second-${order.orderId}-${index}`"
              class="table-row"
              :class="{ 'even': index % 2 === 0, 'odd': index % 2 === 1 }"
            >
              <div class="table-cell order-id">
                <span class="order-number">{{ order.orderId }}</span>
              </div>
              <div class="table-cell origin">
                <span class="location-tag origin-tag">{{ order.origin }}</span>
              </div>
              <div class="table-cell destination">
                <span class="location-tag destination-tag">{{ order.destination }}</span>
              </div>
              <div class="table-cell status">
                <span class="status-badge" :class="getStatusClass(order.status)">
                  {{ order.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 控制按钮 -->
    <div class="controls" v-if="showControls">
      <button @click="togglePause" class="control-btn">
        {{ isPaused ? '▶️' : '⏸️' }}
      </button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props 定义
const props = defineProps({
  // 列表数据
  listData: {
    type: Array,
    default: () => [
      { orderId: 'SH20240715001', origin: '上海仓', destination: '北京分拨中心', status: '运输中' },
      { orderId: 'BJ20240715002', origin: '北京仓', destination: '广州分拨中心', status: '已发货' },
      { orderId: 'GZ20240715003', origin: '广州仓', destination: '深圳配送站', status: '配送中' },
      { orderId: 'SZ20240715004', origin: '深圳仓', destination: '上海分拨中心', status: '运输中' },
      { orderId: 'WH20240715005', origin: '武汉仓', destination: '成都分拨中心', status: '已发货' },
      { orderId: 'CD20240715006', origin: '成都仓', destination: '西安配送站', status: '配送中' },
      { orderId: 'XA20240715007', origin: '西安仓', destination: '武汉分拨中心', status: '运输中' },
      { orderId: 'HZ20240715008', origin: '杭州仓', destination: '上海配送站', status: '已送达' },
      { orderId: 'NJ20240715009', origin: '南京仓', destination: '杭州分拨中心', status: '运输中' },
      { orderId: 'TJ20240715010', origin: '天津仓', destination: '北京配送站', status: '配送中' },
      { orderId: 'CQ20240715011', origin: '重庆仓', destination: '成都分拨中心', status: '已发货' },
      { orderId: 'QD20240715012', origin: '青岛仓', destination: '天津分拨中心', status: '运输中' }
    ]
  },
  
  // 组件标题
  title: {
    type: String,
    default: '实时订单流水'
  },
  
  // 组件副标题
  subtitle: {
    type: String,
    default: ''
  },
  
  // 滚动速度（秒）
  scrollDuration: {
    type: Number,
    default: 30
  },
  
  // 显示的最大行数
  maxRows: {
    type: Number,
    default: 8
  },
  
  // 是否显示控制按钮
  showControls: {
    type: Boolean,
    default: false
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const isPaused = ref(false)

// 计算属性
const displayData = computed(() => {
  return props.listData.slice(0, Math.max(props.maxRows, props.listData.length))
})

const scrollStyle = computed(() => {
  const duration = props.scrollDuration
  return {
    animationDuration: `${duration}s`,
    animationPlayState: isPaused.value ? 'paused' : 'running'
  }
})

// 方法
const getStatusClass = (status) => {
  const statusMap = {
    '已发货': 'shipped',
    '运输中': 'in-transit',
    '配送中': 'delivering',
    '已送达': 'delivered',
    '异常': 'exception'
  }
  return statusMap[status] || 'default'
}

const togglePause = () => {
  isPaused.value = !isPaused.value
}

onMounted(() => {
  console.log('RealTimeOrderList mounted with', displayData.value.length, 'orders')
})
</script>

<style scoped>
.real-time-order-list {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.real-time-order-list:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.4);
  border-color: #00CED1;
}

/* 组件标题 */
.list-header {
  margin-bottom: 12px;
  text-align: center;
  flex-shrink: 0;
}

.list-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0 0 4px 0;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.3);
}

.list-subtitle {
  font-size: 0.85rem;
  color: #7BDEFF;
  opacity: 0.8;
  margin: 0;
}

/* 表格容器 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 固定表头 */
.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr;
  gap: 8px;
  padding: 12px 8px;
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 6px;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.header-cell {
  font-size: 0.85rem;
  font-weight: 600;
  color: #7BDEFF;
  text-align: center;
  padding: 4px;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.scroll-container.paused .scroll-content {
  animation-play-state: paused !important;
}

.scroll-content {
  animation: scrollUp linear infinite;
  animation-fill-mode: forwards;
}

/* 数据组 */
.data-group {
  width: 100%;
}

/* 表格行 */
.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 2px;
  transition: all 0.3s ease;
}

.table-row.even {
  background: rgba(30, 144, 255, 0.1);
}

.table-row.odd {
  background: rgba(14, 38, 92, 0.3);
}

.table-row:hover {
  background: rgba(0, 206, 209, 0.2);
  transform: translateX(4px);
}

/* 表格单元格 */
.table-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  font-size: 0.8rem;
}

.table-cell.order-id {
  justify-content: flex-start;
}

/* 订单号样式 */
.order-number {
  font-family: 'Courier New', monospace;
  color: #FFD700;
  font-weight: 600;
  font-size: 0.75rem;
}

/* 位置标签 */
.location-tag {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.origin-tag {
  background: rgba(0, 255, 127, 0.2);
  color: #00FF7F;
  border: 1px solid rgba(0, 255, 127, 0.3);
}

.destination-tag {
  background: rgba(255, 215, 0, 0.2);
  color: #FFD700;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 状态徽章 */
.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
}

.status-badge.shipped {
  background: rgba(30, 144, 255, 0.2);
  color: #1E90FF;
  border: 1px solid rgba(30, 144, 255, 0.3);
}

.status-badge.in-transit {
  background: rgba(255, 165, 0, 0.2);
  color: #FFA500;
  border: 1px solid rgba(255, 165, 0, 0.3);
}

.status-badge.delivering {
  background: rgba(0, 206, 209, 0.2);
  color: #00CED1;
  border: 1px solid rgba(0, 206, 209, 0.3);
}

.status-badge.delivered {
  background: rgba(0, 255, 127, 0.2);
  color: #00FF7F;
  border: 1px solid rgba(0, 255, 127, 0.3);
}

.status-badge.exception {
  background: rgba(255, 107, 107, 0.2);
  color: #FF6B6B;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* 滚动动画 */
@keyframes scrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

/* 控制按钮 */
.controls {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
}

.control-btn {
  background: rgba(30, 144, 255, 0.8);
  border: 1px solid #1E90FF;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(30, 144, 255, 1);
  transform: scale(1.1);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(14, 38, 92, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(30, 144, 255, 0.3);
  border-top: 3px solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: #7BDEFF;
  font-size: 0.9rem;
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .real-time-order-list {
    padding: 12px;
  }
  
  .list-title {
    font-size: 1rem;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1.8fr 1.2fr 1.2fr 0.8fr;
    gap: 6px;
  }
  
  .header-cell,
  .table-cell {
    font-size: 0.75rem;
  }
}

@media (max-width: 768px) {
  .real-time-order-list {
    padding: 8px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 4px;
  }
  
  .header-cell,
  .table-cell {
    font-size: 0.7rem;
    padding: 2px;
  }
  
  .location-tag,
  .status-badge {
    font-size: 0.65rem;
    padding: 1px 4px;
  }
}

/* 深色主题适配 */
.real-time-order-list {
  color: #ffffff;
}
</style>
