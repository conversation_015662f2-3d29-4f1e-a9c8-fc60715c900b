<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <div class="error-text">
        <h2>出现错误</h2>
        <p class="error-message">{{ message }}</p>
        <div class="error-actions">
          <button @click="retryAction" class="retry-btn">
            <i class="fas fa-redo"></i>
            重新加载
          </button>
          <button @click="refreshPage" class="refresh-btn">
            <i class="fas fa-sync"></i>
            刷新页面
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDashboardStore } from '../stores/dashboard'

// 定义props
interface Props {
  message: string
}

defineProps<Props>()

// 获取store实例
const dashboardStore = useDashboardStore()

// 重试操作
const retryAction = () => {
  console.log('🔄 用户点击重试按钮')
  dashboardStore.loadDashboardData()
}

// 刷新页面
const refreshPage = () => {
  console.log('🔄 用户点击刷新页面')
  window.location.reload()
}
</script>

<style scoped>
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
  padding: 2rem;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 4rem;
  color: #FF6B6B;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.error-text h2 {
  color: #FF6B6B;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.error-message {
  color: #FFB6C1;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
  line-height: 1.5;
  background: rgba(255, 107, 107, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.retry-btn,
.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.retry-btn {
  background: linear-gradient(135deg, #00CED1 0%, #20B2AA 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(0, 206, 209, 0.3);
}

.retry-btn:hover {
  background: linear-gradient(135deg, #20B2AA 0%, #008B8B 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 206, 209, 0.4);
}

.refresh-btn {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #333333;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.refresh-btn:hover {
  background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.retry-btn:active,
.refresh-btn:active {
  transform: translateY(0);
}

/* 添加进入动画 */
.error-content {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 1rem;
  }
  
  .error-icon {
    font-size: 3rem;
  }
  
  .error-text h2 {
    font-size: 1.5rem;
  }
  
  .error-message {
    font-size: 1rem;
    padding: 0.75rem;
  }
  
  .error-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .retry-btn,
  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .error-container {
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  }
  
  .refresh-btn {
    color: #1a1a1a;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .error-icon {
    animation: none;
  }
  
  .error-content {
    animation: none;
  }
  
  .retry-btn:hover,
  .refresh-btn:hover {
    transform: none;
  }
}
</style>
