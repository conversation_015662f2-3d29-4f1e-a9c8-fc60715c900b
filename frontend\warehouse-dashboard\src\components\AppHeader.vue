<template>
  <header class="main-header">
    <div class="flex justify-between items-center mb-8">
      <div class="header-left">
        <div class="time-display">{{ currentTime }}</div>
      </div>
      <div class="header-center">
        <h1 class="text-3xl font-bold text-slate-900">{{ title }}</h1>
      </div>
      <div class="header-right">
        <div class="flex items-center gap-4">
          <div class="user-info">
            <span class="welcome-text">欢迎使用</span>
            <span class="system-name">物流管理系统</span>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: '物流大数据展示平台'
  }
})

// 响应式数据
const currentTime = ref('')
const timer = ref(null)

// 工具函数：格式化数字，前面补0
function fillZero(num) {
  return num < 10 ? '0' + num : String(num)
}

// 更新时间的函数
function updateTime() {
  const myDate = new Date()
  const myYear = myDate.getFullYear()
  const myMonth = myDate.getMonth() + 1
  const myToday = myDate.getDate()
  const myHour = myDate.getHours()
  const myMinute = myDate.getMinutes()
  const mySecond = myDate.getSeconds()
  const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const myDay = myDate.getDay()

  currentTime.value = `${myYear}-${fillZero(myMonth)}-${fillZero(myToday)} ${fillZero(myHour)}:${fillZero(myMinute)}:${fillZero(mySecond)} ${week[myDay]}`
}

// 组件挂载时启动定时器
onMounted(() => {
  updateTime()
  timer.value = setInterval(updateTime, 1000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})

// 暴露方法供父组件调用
defineExpose({
  updateTime,
  currentTime
})
</script>

<style scoped>
/* 顶部 Header 样式 */
.main-header {
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: linear-gradient(135deg, rgba(30, 144, 255, 0.1) 0%, rgba(14, 38, 92, 0.3) 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding: 0 20px;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  box-shadow: 0 2px 10px rgba(30, 144, 255, 0.2);
}

/* 中间标题 */
.header-center h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin: 0;
  letter-spacing: 2px;
}

/* 左侧和右侧区域 */
.header-left, .header-right {
  flex: 1;
  font-size: 1rem;
  color: #7BDEFF;
}

.header-right {
  text-align: right;
}

/* 时间显示 */
.time-display {
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  font-weight: 500;
  color: #7BDEFF;
  text-shadow: 0 0 5px rgba(123, 222, 255, 0.5);
}

/* 用户信息区域 */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.welcome-text {
  font-size: 0.9rem;
  color: #7BDEFF;
  opacity: 0.8;
}

.system-name {
  font-size: 1rem;
  color: #ffffff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    height: 60px;
    padding: 0 15px;
  }
  
  .header-center h1 {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }
  
  .time-display {
    font-size: 0.9rem;
  }
  
  .user-info {
    display: none; /* 在小屏幕上隐藏用户信息 */
  }
}

@media (max-width: 480px) {
  .header-center h1 {
    font-size: 1.2rem;
  }
  
  .time-display {
    font-size: 0.8rem;
  }
}

/* 动画效果 */
.main-header {
  transition: all 0.3s ease;
}

.time-display {
  transition: color 0.3s ease;
}

.time-display:hover {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.8);
}
</style>
