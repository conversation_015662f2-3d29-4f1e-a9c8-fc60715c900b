<template>
  <div class="simple-big-screen">
    <div class="screen-header">
      <h1>🖥️ 大屏展示模式测试</h1>
      <div class="time-display">{{ currentTime }}</div>
    </div>
    
    <div class="screen-content">
      <div class="left-panel">
        <h3>左侧面板</h3>
        <p>数据展示区域</p>
      </div>
      
      <div class="center-area">
        <h2>中央地图区域</h2>
        <div class="map-placeholder">
          <p>地图组件位置</p>
          <p>{{ mapData.warehouses.length }} 个仓库节点</p>
          <p>{{ mapData.routes.length }} 条运输路线</p>
        </div>
      </div>
      
      <div class="right-panel">
        <h3>右侧面板</h3>
        <p>实时数据区域</p>
      </div>
    </div>
    
    <div class="screen-footer">
      <button @click="switchToNormalView" class="back-btn">
        返回配置模式
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 定义事件
const emit = defineEmits(['switchToNormalView'])

// 响应式数据
const currentTime = ref('')

// 模拟地图数据
const mapData = ref({
  warehouses: [
    { name: '北京', value: [116.40, 39.90, 150] },
    { name: '上海', value: [121.47, 31.23, 200] },
    { name: '广州', value: [113.23, 23.16, 180] },
    { name: '深圳', value: [114.07, 22.62, 220] },
    { name: '武汉', value: [114.31, 30.52, 120] }
  ],
  routes: [
    { coords: [[116.40, 39.90], [121.47, 31.23]] },
    { coords: [[121.47, 31.23], [113.23, 23.16]] },
    { coords: [[113.23, 23.16], [114.07, 22.62]] }
  ]
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN')
}

const switchToNormalView = () => {
  emit('switchToNormalView')
}

// 生命周期
let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  console.log('SimpleBigScreenView mounted')
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  console.log('SimpleBigScreenView unmounted')
})
</script>

<style scoped>
.simple-big-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
  color: white;
  font-family: "Microsoft YaHei", sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.screen-header {
  height: 80px;
  background: rgba(16, 33, 62, 0.9);
  border-bottom: 2px solid #40e0d0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
}

.screen-header h1 {
  color: #7BDEFF;
  font-size: 2rem;
  margin: 0;
  text-shadow: 0 0 20px rgba(123, 222, 255, 0.6);
}

.time-display {
  font-size: 1.2rem;
  color: #00FF88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
}

.screen-content {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 20px;
  padding: 20px;
}

.left-panel, .right-panel {
  background: rgba(16, 33, 62, 0.8);
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.center-area {
  background: rgba(16, 33, 62, 0.8);
  border: 2px solid rgba(64, 224, 208, 0.4);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.center-area h2 {
  color: #7BDEFF;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-shadow: 0 0 15px rgba(123, 222, 255, 0.6);
}

.map-placeholder {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  width: 80%;
  height: 60%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.map-placeholder p {
  margin: 10px 0;
  color: #B3E5FC;
  font-size: 1.1rem;
}

.screen-footer {
  height: 50px;
  background: rgba(16, 33, 62, 0.9);
  border-top: 1px solid rgba(64, 224, 208, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30px;
}

.back-btn {
  padding: 10px 20px;
  background: rgba(64, 224, 208, 0.2);
  border: 1px solid rgba(64, 224, 208, 0.4);
  border-radius: 6px;
  color: #7BDEFF;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.back-btn:hover {
  background: rgba(64, 224, 208, 0.3);
  border-color: rgba(64, 224, 208, 0.6);
  transform: translateY(-2px);
}

.left-panel h3, .right-panel h3 {
  color: #7BDEFF;
  margin-top: 0;
  margin-bottom: 15px;
}

.left-panel p, .right-panel p {
  color: #B3E5FC;
  margin: 10px 0;
}
</style>
