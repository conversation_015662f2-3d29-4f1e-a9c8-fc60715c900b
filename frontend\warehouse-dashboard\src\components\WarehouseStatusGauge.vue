<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <div class="gauge-container" :id="chartId" ref="chartContainer"></div>
      <div class="status-info">
        <div class="info-item">
          <span class="label">当前利用率</span>
          <span class="value">{{ utilizationRate }}%</span>
        </div>
        <div class="info-item">
          <span class="label">剩余库容</span>
          <span class="value">{{ remainingCapacity }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '仓储状态监控'
  },
  chartId: {
    type: String,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const chartInstance = ref(null)
const chartContainer = ref(null)

// 计算利用率和剩余库容
const utilizationRate = computed(() => {
  return props.data.utilization_rate || 78.5
})

const remainingCapacity = computed(() => {
  return props.data.remaining_capacity || '2.1万m³'
})

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance.value = echarts.init(chartContainer.value)
  updateChart()
  window.addEventListener('resize', handleResize)
}

const updateChart = () => {
  if (!chartInstance.value) return
  
  const rate = utilizationRate.value
  
  const option = {
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        radius: '80%',
        min: 0,
        max: 100,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            width: 15,
            color: [
              [0.3, '#32CD32'],
              [0.7, '#FFD700'],
              [1, '#FF6347']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: '#1E90FF'
          }
        },
        axisTick: {
          distance: -15,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -20,
          length: 15,
          lineStyle: {
            color: '#fff',
            width: 3
          }
        },
        axisLabel: {
          color: '#7BDEFF',
          distance: 25,
          fontSize: 12
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
          color: '#ffffff',
          fontSize: 20,
          offsetCenter: [0, '70%']
        },
        data: [
          {
            value: rate,
            name: '仓储利用率'
          }
        ],
        title: {
          color: '#7BDEFF',
          fontSize: 14,
          offsetCenter: [0, '90%']
        }
      }
    ]
  }
  
  chartInstance.value.setOption(option, true)
}

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.gauge-container {
  flex: 1;
  width: 100%;
  min-height: 200px;
}

.status-info {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  border-top: 1px solid rgba(30, 144, 255, 0.3);
  margin-top: 10px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.info-item .label {
  font-size: 0.9rem;
  color: #7BDEFF;
}

.info-item .value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gauge-container {
    min-height: 150px;
  }
  
  .info-item .value {
    font-size: 1rem;
  }
}
</style>
