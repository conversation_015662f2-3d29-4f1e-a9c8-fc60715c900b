# 第二阶段功能测试指南

## 🎯 测试目标
验证动态添加和移除组件的核心功能是否正常工作。

## 🧪 测试步骤

### 1. 基础界面测试
1. 打开浏览器访问 http://localhost:5173/
2. 点击"动态仪表盘"按钮
3. 验证界面布局：
   - ✅ 左侧显示组件工具箱
   - ✅ 右侧显示主画布区域
   - ✅ 顶部显示标题和控制按钮

### 2. 组件工具箱测试
1. 检查左侧工具箱内容：
   - ✅ 显示"组件库"标题
   - ✅ 显示6个可拖拽的组件项
   - ✅ 每个组件显示图标、标题和描述
   - ✅ 底部显示使用说明

2. 交互测试：
   - ✅ 鼠标悬停时组件高亮
   - ✅ 鼠标按下时组件缩放效果

### 3. 拖拽添加功能测试
1. **基本拖拽测试**：
   - 从工具箱拖拽"基本信息"到画布
   - 验证组件成功添加到画布
   - 验证组件显示正确的内容

2. **多组件添加测试**：
   - 依次拖拽不同类型的组件
   - 验证每个组件都能正确添加
   - 验证组件不会重叠

3. **拖拽视觉反馈测试**：
   - 拖拽时画布显示绿色虚线边框
   - 拖拽时显示蓝色占位符
   - 释放后组件正确定位

### 4. 组件删除功能测试
1. **删除按钮测试**：
   - 验证每个组件右上角显示红色"×"按钮
   - 点击删除按钮
   - 验证组件从画布中移除

2. **批量删除测试**：
   - 添加多个组件
   - 逐一删除所有组件
   - 验证画布变为空白状态

### 5. 布局调整功能测试
1. **拖拽移动测试**：
   - 拖拽现有组件到新位置
   - 验证其他组件自动调整位置
   - 验证布局保持整齐

2. **调整大小测试**：
   - 拖拽组件边角调整大小
   - 验证组件内容正确缩放
   - 验证其他组件自动重排

### 6. 数据持久化测试
1. **自动保存测试**：
   - 添加几个组件并调整布局
   - 刷新页面
   - 验证布局完全恢复

2. **清空布局测试**：
   - 点击"清空布局"按钮
   - 验证所有组件被移除
   - 刷新页面验证清空状态保持

3. **重置布局测试**：
   - 点击"重置布局"按钮
   - 验证恢复到默认的3个组件布局

### 7. 响应式测试
1. **窗口大小调整**：
   - 调整浏览器窗口大小
   - 验证组件自动适应新尺寸
   - 验证工具箱保持固定宽度

2. **移动设备模拟**：
   - 使用浏览器开发者工具模拟移动设备
   - 验证界面在小屏幕上的表现

## 🔍 预期结果

### 成功标准
- [ ] 所有组件都能从工具箱成功拖拽到画布
- [ ] 删除按钮能正确移除组件
- [ ] 布局变化能实时保存到localStorage
- [ ] 页面刷新后布局完全恢复
- [ ] 拖拽和调整大小功能流畅无卡顿
- [ ] 视觉反馈清晰明确

### 性能指标
- 拖拽响应时间 < 100ms
- 组件渲染时间 < 200ms
- 布局保存时间 < 50ms
- 页面加载时间 < 2s

## 🐛 常见问题排查

### 问题1：组件无法拖拽
**可能原因**：
- GridStack未正确初始化
- acceptWidgets配置错误
- CSS样式冲突

**解决方法**：
- 检查浏览器控制台错误
- 验证GridStack配置
- 检查CSS类名

### 问题2：组件删除后仍显示
**可能原因**：
- Vue组件实例未正确清理
- GridStack元素未移除
- Store状态未更新

**解决方法**：
- 检查componentInstances清理
- 验证removeWidget逻辑
- 查看Store状态变化

### 问题3：布局不保存
**可能原因**：
- localStorage权限问题
- watch监听器未触发
- 序列化错误

**解决方法**：
- 检查localStorage可用性
- 验证watch配置
- 查看控制台错误信息

## 📊 测试报告模板

```
测试日期：____
测试人员：____
浏览器版本：____

功能测试结果：
□ 界面布局 - 通过/失败
□ 组件拖拽 - 通过/失败  
□ 组件删除 - 通过/失败
□ 布局调整 - 通过/失败
□ 数据持久化 - 通过/失败
□ 响应式设计 - 通过/失败

发现的问题：
1. ________________
2. ________________
3. ________________

总体评价：
□ 优秀 □ 良好 □ 一般 □ 需改进

建议：
________________
```

## 🚀 下一步计划

测试通过后，可以考虑以下增强功能：
1. 添加更多组件类型
2. 实现组件配置面板
3. 添加拖拽动画效果
4. 支持组件分组
5. 实现布局模板功能
