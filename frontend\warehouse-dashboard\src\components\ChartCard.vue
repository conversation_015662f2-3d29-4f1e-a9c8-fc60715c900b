<template>
  <div class="bg-slate-700 rounded-lg overflow-hidden flex flex-col">
    <!-- Card Header -->
    <div class="bg-blue-600 p-3">
      <h3 class="font-semibold text-white truncate">{{ title }}</h3>
    </div>

    <!-- Card Body -->
    <div class="p-4 flex-grow">
      <!-- The slot for chart or content -->
      <div v-if="options && chartId" :id="chartId" class="chart w-full h-full"></div>
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import * as echarts from 'echarts'

// 定义 props 接口
interface Props {
  options?: Record<string, any>
  chartId?: string
  title?: string
}

// 定义 props
const props = withDefaults(defineProps<Props>(), {
  title: '图表',
  chartId: () => `chart-${Math.random().toString(36).substr(2, 9)}`
})

// 图表实例引用
const chartInstance = ref<echarts.ECharts | null>(null)

// 初始化图表
const initChart = (): void => {
  if (!props.options || !props.chartId) return

  const chartDom = document.getElementById(props.chartId)
  if (chartDom) {
    chartInstance.value = echarts.init(chartDom)
    chartInstance.value.setOption(props.options)

    // 监听窗口大小变化，自动调整图表大小
    window.addEventListener('resize', handleResize)
  }
}

// 处理窗口大小变化
const handleResize = (): void => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 更新图表配置
const updateChart = (newOptions: Record<string, any>): void => {
  if (chartInstance.value) {
    chartInstance.value.setOption(newOptions, true)
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法供父组件调用
defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
/* 图表样式 */
.chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart {
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .chart {
    min-height: 200px;
  }
}
</style>
