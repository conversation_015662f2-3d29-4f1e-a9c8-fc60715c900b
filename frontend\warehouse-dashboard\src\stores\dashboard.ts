import { defineStore } from 'pinia'
import type {
  DashboardState,
  RegionName,
  KeyMetrics,
  MonthlyOrders,
  LogisticsData,
  DataCompleteness,
  WarehouseStatus,
  SelectedRegionInfo,
  RealTimeUpdateEvent,
  DateRange
} from '../types/index'
import {
  fetchKeyMetrics,
  fetchSalesDistribution,
  fetchMonthlyOrders,
  fetchLogisticsData,
  fetchRealTimeOrders,
  subscribeToRealTimeUpdates
} from '../mockApi'

export const useDashboardStore = defineStore('dashboard', {
  // 状态定义
  state: (): DashboardState => ({
    // 加载和错误状态
    isLoading: false,
    error: null,

    // API数据
    keyMetricsData: null,
    salesDistributionData: null,
    monthlyOrdersData: null,
    logisticsData: null,
    realTimeOrdersData: null,

    // 筛选状态
    selectedRegion: '总计',
    startDate: null,
    endDate: null,

    // 时间相关状态
    currentTime: '',
    lastUpdateTime: '',
    onlineWarehouses: 4,
    totalWarehouses: 4,

    // 实时更新订阅
    unsubscribeRealTimeUpdates: null
  }),

  // 计算属性
  getters: {
    // 获取加载状态
    isDataLoaded: (state): boolean => {
      return !state.isLoading && !state.error && state.keyMetricsData !== null
    },

    // 获取数据完整性状态
    dataCompleteness: (state): DataCompleteness => {
      const dataFields: (keyof DashboardState)[] = [
        'keyMetricsData',
        'salesDistributionData',
        'monthlyOrdersData',
        'logisticsData',
        'realTimeOrdersData'
      ]

      const loadedCount = dataFields.filter(field => state[field] !== null).length
      return {
        loaded: loadedCount,
        total: dataFields.length,
        percentage: Math.round((loadedCount / dataFields.length) * 100)
      }
    },

    // 获取仓库在线状态
    warehouseStatus: (state): WarehouseStatus => {
      return {
        online: state.onlineWarehouses,
        total: state.totalWarehouses,
        percentage: Math.round((state.onlineWarehouses / state.totalWarehouses) * 100)
      }
    },

    // 筛选后的关键指标数据
    filteredKeyMetrics: (state): KeyMetrics | null => {
      if (!state.keyMetricsData || !state.selectedRegion) {
        return null
      }
      return state.keyMetricsData[state.selectedRegion] || null
    },

    // 筛选后的月度订单数据
    filteredMonthlyOrders: (state): MonthlyOrders | null => {
      if (!state.monthlyOrdersData || !state.selectedRegion) {
        return null
      }

      const regionData = state.monthlyOrdersData[state.selectedRegion]
      if (!regionData) return null

      // 如果没有设置日期筛选，返回原始数据
      if (!state.startDate && !state.endDate) {
        return regionData
      }

      // 应用日期筛选
      const filteredIndices: number[] = []
      regionData.dates.forEach((date, index) => {
        const currentDate = new Date(date)
        const startDate = state.startDate ? new Date(state.startDate) : null
        const endDate = state.endDate ? new Date(state.endDate) : null

        let includeDate = true

        if (startDate && currentDate < startDate) {
          includeDate = false
        }

        if (endDate && currentDate > endDate) {
          includeDate = false
        }

        if (includeDate) {
          filteredIndices.push(index)
        }
      })

      // 构建筛选后的数据
      return {
        xAxisData: filteredIndices.map(i => regionData.xAxisData[i]),
        seriesData: filteredIndices.map(i => regionData.seriesData[i]),
        dates: filteredIndices.map(i => regionData.dates[i])
      }
    },

    // 筛选后的物流数据
    filteredLogisticsData: (state): LogisticsData | null => {
      if (!state.logisticsData || !state.selectedRegion) {
        return null
      }

      const regionData = state.logisticsData[state.selectedRegion]
      if (!regionData) return null

      // 如果没有设置日期筛选，返回原始数据
      if (!state.startDate && !state.endDate) {
        return regionData
      }

      // 应用日期筛选
      const filterByDate = (items: any[]) => {
        return items.filter(item => {
          const currentDate = new Date(item.date)
          const startDate = state.startDate ? new Date(state.startDate) : null
          const endDate = state.endDate ? new Date(state.endDate) : null

          let includeDate = true

          if (startDate && currentDate < startDate) {
            includeDate = false
          }

          if (endDate && currentDate > endDate) {
            includeDate = false
          }

          return includeDate
        })
      }

      // 筛选仓库数据和路线数据
      return {
        warehouseData: filterByDate(regionData.warehouseData),
        routeData: filterByDate(regionData.routeData)
      }
    },

    // 获取可用的区域列表
    availableRegions: (state): RegionName[] => {
      if (!state.keyMetricsData) {
        return []
      }
      return Object.keys(state.keyMetricsData).filter(region => region !== '总计') as RegionName[]
    },

    // 获取当前选中区域的信息
    selectedRegionInfo: (state): SelectedRegionInfo => {
      return {
        name: state.selectedRegion,
        isTotal: state.selectedRegion === '总计'
      }
    },

    // 获取当前日期范围信息
    currentDateRange: (state): DateRange => {
      return {
        start: state.startDate,
        end: state.endDate
      }
    },

    // 检查是否有活跃的筛选条件
    hasActiveFilters: (state): boolean => {
      const hasRegionFilter = state.selectedRegion !== '总计'
      const hasDateFilter = state.startDate !== null || state.endDate !== null
      return hasRegionFilter || hasDateFilter
    }
  },

  // 动作定义
  actions: {
    // 加载仪表盘数据
    async loadDashboardData(): Promise<void> {
      // 立即设置加载状态
      this.isLoading = true
      this.error = null

      try {
        console.log('🔄 开始加载仪表盘数据...')

        // 使用 Promise.all 并发请求所有API数据
        const [
          keyMetrics,
          salesDistribution,
          monthlyOrders,
          logistics,
          realTimeOrders
        ] = await Promise.all([
          fetchKeyMetrics(),
          fetchSalesDistribution(),
          fetchMonthlyOrders(),
          fetchLogisticsData(),
          fetchRealTimeOrders()
        ])

        // 将返回的数据分别赋值给对应的状态
        this.keyMetricsData = keyMetrics
        this.salesDistributionData = salesDistribution
        this.monthlyOrdersData = monthlyOrders
        this.logisticsData = logistics
        this.realTimeOrdersData = realTimeOrders

        // 更新最后更新时间
        const now = new Date()
        this.lastUpdateTime = now.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit'
        })

        console.log('✅ 仪表盘数据加载完成')

        // 成功后设置加载状态为false
        this.isLoading = false

      } catch (err: any) {
        console.error('❌ 仪表盘数据加载失败:', err)
        this.error = '数据加载失败，请稍后重试。'
        this.isLoading = false
      }
    },

    // 重新加载数据
    async retryLoadData(): Promise<void> {
      console.log('🔄 重新加载数据...')
      await this.loadDashboardData()
    },

    // 选择区域进行筛选
    selectRegion(regionName: RegionName): void {
      console.log('🎯 选择区域:', regionName)
      this.selectedRegion = regionName
    },

    // 设置日期范围筛选
    setDateRange(dateRange: DateRange): void {
      console.log('📅 设置日期范围:', dateRange)
      this.startDate = dateRange.start
      this.endDate = dateRange.end
    },

    // 定期刷新数据
    async refreshData(): Promise<void> {
      try {
        console.log('🔄 定期刷新数据...')

        // 只刷新实时订单数据，避免频繁刷新所有数据
        const newRealTimeOrders = await fetchRealTimeOrders()
        this.realTimeOrdersData = newRealTimeOrders

        // 更新最后更新时间
        const now = new Date()
        this.lastUpdateTime = now.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit'
        })

      } catch (err: any) {
        console.error('❌ 数据刷新失败:', err)
      }
    },

    // 更新当前时间
    updateCurrentTime(): void {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 订阅实时数据更新
    subscribeToRealTime(): void {
      // 如果已经有订阅，先取消
      if (this.unsubscribeRealTimeUpdates) {
        this.unsubscribeRealTimeUpdates()
      }

      // 订阅实时数据更新
      this.unsubscribeRealTimeUpdates = subscribeToRealTimeUpdates((update: RealTimeUpdateEvent) => {
        if (update.type === 'orders') {
          this.realTimeOrdersData = update.data
          console.log('📡 实时订单数据已更新')
        }
      })
    },

    // 取消实时数据订阅
    unsubscribeFromRealTime(): void {
      if (this.unsubscribeRealTimeUpdates) {
        this.unsubscribeRealTimeUpdates()
        this.unsubscribeRealTimeUpdates = null
        console.log('🔌 已取消实时数据订阅')
      }
    },

    // 重置所有数据
    resetData() {
      this.isLoading = true
      this.error = null
      this.keyMetricsData = null
      this.salesDistributionData = null
      this.monthlyOrdersData = null
      this.logisticsData = null
      this.realTimeOrdersData = null
      this.currentTime = ''
      this.lastUpdateTime = ''
      
      console.log('🧹 数据已重置')
    },

    // 更新仓库在线状态
    updateWarehouseStatus(online: number, total: number) {
      this.onlineWarehouses = online
      this.totalWarehouses = total
    },

    // 手动更新特定数据
    async updateKeyMetrics() {
      try {
        const newData = await fetchKeyMetrics()
        this.keyMetricsData = newData
        console.log('📊 KPI数据已更新')
      } catch (err) {
        console.error('❌ KPI数据更新失败:', err)
      }
    },

    async updateSalesDistribution() {
      try {
        const newData = await fetchSalesDistribution()
        this.salesDistributionData = newData
        console.log('💰 销售分布数据已更新')
      } catch (err) {
        console.error('❌ 销售分布数据更新失败:', err)
      }
    },

    async updateMonthlyOrders() {
      try {
        const newData = await fetchMonthlyOrders()
        this.monthlyOrdersData = newData
        console.log('📈 月度订单数据已更新')
      } catch (err) {
        console.error('❌ 月度订单数据更新失败:', err)
      }
    },

    async updateLogisticsData() {
      try {
        const newData = await fetchLogisticsData()
        this.logisticsData = newData
        console.log('🗺️ 物流数据已更新')
      } catch (err) {
        console.error('❌ 物流数据更新失败:', err)
      }
    },

    async updateRealTimeOrders() {
      try {
        const newData = await fetchRealTimeOrders()
        this.realTimeOrdersData = newData
        console.log('📋 实时订单数据已更新')
      } catch (err) {
        console.error('❌ 实时订单数据更新失败:', err)
      }
    }
  }
})
