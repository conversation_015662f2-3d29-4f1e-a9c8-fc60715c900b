<template>
  <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200 transition-all duration-300 hover:shadow-lg">
    <!-- 图表标题 -->
    <div class="map-header mb-4">
      <h3 class="text-lg font-semibold text-slate-800 mb-2">{{ title }}</h3>
      <div class="text-sm text-slate-600" v-if="subtitle">{{ subtitle }}</div>
    </div>
    
    <!-- ECharts 容器 -->
    <div 
      ref="mapContainer" 
      class="map-container"
      :style="{ width: '100%', height: mapHeight }"
    ></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">地图加载中...</div>
    </div>
    
    <!-- 图例 -->
    <div class="map-legend">
      <div class="legend-item">
        <div class="legend-dot warehouse"></div>
        <span>仓库节点</span>
      </div>
      <div class="legend-item">
        <div class="legend-line"></div>
        <span>运输路线</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props 定义
const props = defineProps({
  // 仓库数据
  warehouseData: {
    type: Array,
    default: () => [
      { name: '北京', value: [116.40, 39.90, 150] },
      { name: '上海', value: [121.47, 31.23, 200] },
      { name: '广州', value: [113.23, 23.16, 180] },
      { name: '深圳', value: [114.07, 22.62, 220] },
      { name: '武汉', value: [114.31, 30.52, 120] },
      { name: '成都', value: [104.06, 30.67, 160] },
      { name: '西安', value: [108.95, 34.27, 100] },
      { name: '杭州', value: [120.19, 30.26, 140] }
    ]
  },
  
  // 路线数据
  routeData: {
    type: Array,
    default: () => [
      { fromName: '北京', toName: '上海', coords: [[116.40, 39.90], [121.47, 31.23]] },
      { fromName: '上海', toName: '广州', coords: [[121.47, 31.23], [113.23, 23.16]] },
      { fromName: '广州', toName: '深圳', coords: [[113.23, 23.16], [114.07, 22.62]] },
      { fromName: '北京', toName: '武汉', coords: [[116.40, 39.90], [114.31, 30.52]] },
      { fromName: '武汉', toName: '成都', coords: [[114.31, 30.52], [104.06, 30.67]] },
      { fromName: '北京', toName: '西安', coords: [[116.40, 39.90], [108.95, 34.27]] },
      { fromName: '上海', toName: '杭州', coords: [[121.47, 31.23], [120.19, 30.26]] }
    ]
  },
  
  // 图表标题
  title: {
    type: String,
    default: '全国物流网络'
  },
  
  // 图表副标题
  subtitle: {
    type: String,
    default: ''
  },
  
  // 地图高度
  mapHeight: {
    type: String,
    default: '400px'
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  
  // 是否启用动画
  enableAnimation: {
    type: Boolean,
    default: true
  }
})

// 响应式数据
const mapContainer = ref(null)
const mapInstance = ref(null)

// 中国地图 GeoJSON 数据（简化版，包含主要省份轮廓）
const chinaGeoJson = {
  type: "FeatureCollection",
  features: [
    // 北京
    {
      type: "Feature",
      properties: { name: "北京" },
      geometry: {
        type: "Polygon",
        coordinates: [[[115.7, 39.4], [117.4, 39.4], [117.4, 41.6], [115.7, 41.6], [115.7, 39.4]]]
      }
    },
    // 上海
    {
      type: "Feature",
      properties: { name: "上海" },
      geometry: {
        type: "Polygon",
        coordinates: [[[120.9, 30.7], [122.2, 30.7], [122.2, 31.9], [120.9, 31.9], [120.9, 30.7]]]
      }
    },
    // 广东
    {
      type: "Feature",
      properties: { name: "广东" },
      geometry: {
        type: "Polygon",
        coordinates: [[[109.7, 20.2], [117.2, 20.2], [117.2, 25.3], [109.7, 25.3], [109.7, 20.2]]]
      }
    },
    // 湖北
    {
      type: "Feature",
      properties: { name: "湖北" },
      geometry: {
        type: "Polygon",
        coordinates: [[[108.3, 29.0], [116.1, 29.0], [116.1, 33.3], [108.3, 33.3], [108.3, 29.0]]]
      }
    },
    // 四川
    {
      type: "Feature",
      properties: { name: "四川" },
      geometry: {
        type: "Polygon",
        coordinates: [[[97.3, 26.0], [108.5, 26.0], [108.5, 34.3], [97.3, 34.3], [97.3, 26.0]]]
      }
    },
    // 陕西
    {
      type: "Feature",
      properties: { name: "陕西" },
      geometry: {
        type: "Polygon",
        coordinates: [[[105.3, 31.4], [111.2, 31.4], [111.2, 39.6], [105.3, 39.6], [105.3, 31.4]]]
      }
    },
    // 浙江
    {
      type: "Feature",
      properties: { name: "浙江" },
      geometry: {
        type: "Polygon",
        coordinates: [[[118.0, 27.0], [123.2, 27.0], [123.2, 31.4], [118.0, 31.4], [118.0, 27.0]]]
      }
    },
    // 江苏
    {
      type: "Feature",
      properties: { name: "江苏" },
      geometry: {
        type: "Polygon",
        coordinates: [[[116.4, 30.8], [121.9, 30.8], [121.9, 35.1], [116.4, 35.1], [116.4, 30.8]]]
      }
    }
  ]
}

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) return
  
  // 销毁已存在的实例
  if (mapInstance.value) {
    mapInstance.value.dispose()
  }
  
  // 注册中国地图
  echarts.registerMap('china', chinaGeoJson)
  
  // 创建新的图表实例
  mapInstance.value = echarts.init(mapContainer.value)
  
  // 设置地图配置
  updateMap()
}

// 更新地图数据
const updateMap = () => {
  if (!mapInstance.value) return
  
  const option = {
    // 背景透明
    backgroundColor: 'transparent',
    
    // 提示框配置
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(14, 38, 92, 0.9)',
      borderColor: '#1E90FF',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: (params) => {
        if (params.seriesType === 'effectScatter') {
          return `
            <div style="padding: 8px;">
              <div style="color: #7BDEFF; font-weight: bold; margin-bottom: 4px;">
                ${params.name}
              </div>
              <div style="display: flex; justify-content: space-between; gap: 20px;">
                <span>业务量:</span>
                <span style="color: #00FF7F; font-weight: bold;">
                  ${params.value[2]} 单/日
                </span>
              </div>
              <div style="display: flex; justify-content: space-between; gap: 20px;">
                <span>坐标:</span>
                <span style="color: #FFD700; font-size: 10px;">
                  ${params.value[0].toFixed(2)}, ${params.value[1].toFixed(2)}
                </span>
              </div>
            </div>
          `
        } else if (params.seriesType === 'lines') {
          const route = props.routeData.find(r => 
            r.fromName === params.data.fromName && r.toName === params.data.toName
          )
          return `
            <div style="padding: 8px;">
              <div style="color: #7BDEFF; font-weight: bold; margin-bottom: 4px;">
                运输路线
              </div>
              <div style="display: flex; justify-content: space-between; gap: 20px;">
                <span>起点:</span>
                <span style="color: #00FF7F;">${route?.fromName || '未知'}</span>
              </div>
              <div style="display: flex; justify-content: space-between; gap: 20px;">
                <span>终点:</span>
                <span style="color: #00FF7F;">${route?.toName || '未知'}</span>
              </div>
            </div>
          `
        }
        return params.name
      }
    },
    
    // 地理坐标系
    geo: {
      map: 'china',
      roam: true, // 允许缩放和平移
      zoom: 1.2,
      center: [104, 35],
      
      // 地图样式
      itemStyle: {
        areaColor: 'rgba(14, 38, 92, 0.3)',
        borderColor: 'rgba(30, 144, 255, 0.5)',
        borderWidth: 1
      },
      
      // 高亮样式
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(30, 144, 255, 0.2)',
          borderColor: '#1E90FF',
          borderWidth: 2
        }
      },
      
      // 标签样式
      label: {
        show: false,
        color: '#7BDEFF',
        fontSize: 10
      }
    },
    
    // 系列配置
    series: [
      // 仓库节点 - 涟漪散点图
      {
        name: '仓库分布',
        type: 'effectScatter',
        coordinateSystem: 'geo',
        data: props.warehouseData,
        
        // 涟漪效果
        rippleEffect: {
          brushType: 'stroke',
          scale: 4,
          period: 3
        },
        
        // 标签配置
        label: {
          show: true,
          position: 'right',
          color: '#7BDEFF',
          fontSize: 11,
          fontFamily: 'Microsoft YaHei',
          formatter: '{b}'
        },
        
        // 样式配置
        itemStyle: {
          color: '#00CED1',
          shadowBlur: 10,
          shadowColor: '#00CED1',
          borderColor: '#ffffff',
          borderWidth: 2
        },
        
        // 高亮样式
        emphasis: {
          itemStyle: {
            color: '#FFD700',
            shadowBlur: 20,
            shadowColor: '#FFD700',
            scale: 1.5
          }
        },
        
        // 符号大小
        symbolSize: (val) => {
          return Math.max(8, Math.min(25, val[2] / 10))
        },
        
        // 动画配置
        animationDuration: props.enableAnimation ? 2000 : 0,
        animationEasing: 'cubicOut'
      },
      
      // 运输路线
      {
        name: '运输路线',
        type: 'lines',
        coordinateSystem: 'geo',
        data: props.routeData,
        
        // 线条样式
        lineStyle: {
          color: '#1E90FF',
          width: 2,
          opacity: 0.8,
          curveness: 0.3 // 曲线弯曲度
        },
        
        // 动态效果
        effect: {
          show: true,
          period: 4,
          trailLength: 0.1,
          color: '#FFD700',
          symbolSize: 6,
          symbol: 'arrow'
        },
        
        // 高亮样式
        emphasis: {
          lineStyle: {
            color: '#00FF7F',
            width: 4,
            opacity: 1
          }
        },
        
        // 动画配置
        animationDuration: props.enableAnimation ? 3000 : 0,
        animationEasing: 'cubicOut',
        animationDelay: (idx) => idx * 200
      }
    ]
  }
  
  // 设置配置并渲染
  mapInstance.value.setOption(option, true)
}

// 响应式调整地图大小
const resizeMap = () => {
  if (mapInstance.value) {
    mapInstance.value.resize()
  }
}

// 监听数据变化
watch(
  () => [props.warehouseData, props.routeData],
  () => {
    nextTick(() => {
      updateMap()
    })
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (newVal) => {
    if (!newVal) {
      nextTick(() => {
        updateMap()
      })
    }
  }
)

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initMap()
    window.addEventListener('resize', resizeMap)
  })
})

onUnmounted(() => {
  if (mapInstance.value) {
    mapInstance.value.dispose()
  }
  window.removeEventListener('resize', resizeMap)
})

// 暴露方法供父组件调用
defineExpose({
  resizeMap,
  updateMap,
  getMapInstance: () => mapInstance.value
})
</script>

<style scoped>
.logistics-map {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.logistics-map:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.4);
  border-color: #00CED1;
}

/* 地图标题 */
.map-header {
  margin-bottom: 12px;
  text-align: center;
}

.map-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0 0 4px 0;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.3);
}

.map-subtitle {
  font-size: 0.85rem;
  color: #7BDEFF;
  opacity: 0.8;
  margin: 0;
}

/* 地图容器 */
.map-container {
  position: relative;
  min-height: 350px;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(14, 38, 92, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(30, 144, 255, 0.3);
  border-top: 3px solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: #7BDEFF;
  font-size: 0.9rem;
}

/* 图例 */
.map-legend {
  position: absolute;
  top: 60px;
  right: 20px;
  background: rgba(14, 38, 92, 0.8);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 6px;
  padding: 12px;
  backdrop-filter: blur(10px);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.8rem;
  color: #7BDEFF;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.warehouse {
  background: #00CED1;
  box-shadow: 0 0 8px rgba(0, 206, 209, 0.6);
  animation: pulse 2s infinite;
}

.legend-line {
  width: 20px;
  height: 2px;
  background: linear-gradient(90deg, #1E90FF, #FFD700);
  position: relative;
}

.legend-line::after {
  content: '';
  position: absolute;
  right: -2px;
  top: -2px;
  width: 0;
  height: 0;
  border-left: 4px solid #FFD700;
  border-top: 3px solid transparent;
  border-bottom: 3px solid transparent;
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .logistics-map {
    padding: 12px;
  }
  
  .map-title {
    font-size: 1rem;
  }
  
  .map-subtitle {
    font-size: 0.8rem;
  }
  
  .map-legend {
    top: 50px;
    right: 15px;
    padding: 8px;
  }
}

@media (max-width: 768px) {
  .logistics-map {
    padding: 8px;
  }
  
  .map-container {
    min-height: 250px;
  }
  
  .map-title {
    font-size: 0.9rem;
  }
  
  .map-legend {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  
  .legend-item {
    margin-bottom: 0;
  }
}

/* 深色主题适配 */
.logistics-map {
  color: #ffffff;
}

/* 确保地图在容器中正确显示 */
.map-container > div {
  width: 100% !important;
  height: 100% !important;
}
</style>
