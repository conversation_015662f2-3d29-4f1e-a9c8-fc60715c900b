<template>
  <div class="cross-filter-test">
    <AppHeader title="交叉筛选功能测试" />
    
    <div class="test-container">
      <div class="controls-section">
        <h2>筛选控制</h2>
        <div class="controls-grid">
          <div class="control-group">
            <label>当前选中区域:</label>
            <div class="current-region">{{ selectedRegion }}</div>
          </div>
          
          <div class="control-group">
            <label>可用区域:</label>
            <div class="region-buttons">
              <button 
                v-for="region in allRegions" 
                :key="region"
                @click="dashboardStore.selectRegion(region)"
                :class="{ active: selectedRegion === region }"
                class="region-btn"
              >
                {{ region }}
              </button>
            </div>
          </div>
          
          <div class="control-group">
            <button @click="loadTestData" class="action-btn">
              重新加载数据
            </button>
          </div>
        </div>
      </div>

      <div class="data-section">
        <h2>筛选后的数据</h2>
        <div class="data-grid">
          <!-- KPI 数据 -->
          <div class="data-card">
            <h3>KPI 指标</h3>
            <div v-if="filteredKeyMetrics" class="data-content">
              <div v-for="(kpi, index) in filteredKeyMetrics" :key="index" class="kpi-item">
                <span class="kpi-title">{{ kpi.title }}:</span>
                <span class="kpi-value">{{ kpi.value }} {{ kpi.unit }}</span>
              </div>
            </div>
            <div v-else class="no-data">暂无数据</div>
          </div>
          
          <!-- 月度订单数据 -->
          <div class="data-card">
            <h3>月度订单</h3>
            <div v-if="filteredMonthlyOrders" class="data-content">
              <div class="chart-preview">
                <div v-for="(month, index) in filteredMonthlyOrders.xAxisData" :key="index" class="month-item">
                  <span class="month-name">{{ month }}:</span>
                  <span class="month-value">{{ filteredMonthlyOrders.seriesData[index] }} 单</span>
                </div>
              </div>
            </div>
            <div v-else class="no-data">暂无数据</div>
          </div>
          
          <!-- 物流数据 -->
          <div class="data-card">
            <h3>物流网络</h3>
            <div v-if="filteredLogisticsData" class="data-content">
              <div class="logistics-info">
                <div class="info-item">
                  <span class="info-label">仓库数量:</span>
                  <span class="info-value">{{ filteredLogisticsData.warehouseData.length }} 个</span>
                </div>
                <div class="info-item">
                  <span class="info-label">运输路线:</span>
                  <span class="info-value">{{ filteredLogisticsData.routeData.length }} 条</span>
                </div>
                <div class="warehouse-list">
                  <div v-for="warehouse in filteredLogisticsData.warehouseData" :key="warehouse.name" class="warehouse-item">
                    {{ warehouse.name }}: {{ warehouse.value[2] }} 单/日
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="no-data">暂无数据</div>
          </div>
        </div>
      </div>

      <div class="status-section">
        <h2>系统状态</h2>
        <div class="status-grid">
          <div class="status-card">
            <div class="status-label">加载状态:</div>
            <div class="status-value" :class="{ loading: isLoading }">
              {{ isLoading ? '加载中...' : '已完成' }}
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-label">错误状态:</div>
            <div class="status-value" :class="{ error: error }">
              {{ error || '正常' }}
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-label">数据完整性:</div>
            <div class="status-value">
              {{ dataCompleteness.percentage }}% ({{ dataCompleteness.loaded }}/{{ dataCompleteness.total }})
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useDashboardStore } from './stores/dashboard.js'
import AppHeader from './components/AppHeader.vue'

// 实例化Dashboard Store
const dashboardStore = useDashboardStore()

// 从Store中解构数据
const {
  isLoading,
  error,
  selectedRegion,
  filteredKeyMetrics,
  filteredMonthlyOrders,
  filteredLogisticsData,
  availableRegions,
  dataCompleteness
} = storeToRefs(dashboardStore)

// 所有区域（包括总计）
const allRegions = computed(() => {
  return ['总计', ...availableRegions.value]
})

// 加载测试数据
const loadTestData = async () => {
  await dashboardStore.loadDashboardData()
}

// 初始化时加载数据
if (!dashboardStore.isDataLoaded) {
  loadTestData()
}
</script>

<style scoped>
.cross-filter-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.controls-section,
.data-section,
.status-section {
  margin-bottom: 40px;
}

.controls-section h2,
.data-section h2,
.status-section h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  background: rgba(14, 38, 92, 0.6);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #1E90FF;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  color: #7BDEFF;
  font-size: 0.9rem;
  font-weight: 600;
}

.current-region {
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid #1E90FF;
  color: #FFD700;
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: 600;
}

.region-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.region-btn {
  background: rgba(30, 144, 255, 0.3);
  color: #ffffff;
  border: 1px solid #1E90FF;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.region-btn:hover {
  background: rgba(30, 144, 255, 0.6);
  transform: translateY(-1px);
}

.region-btn.active {
  background: rgba(255, 215, 0, 0.8);
  color: #000;
  border-color: #FFD700;
}

.action-btn {
  background: rgba(0, 255, 127, 0.8);
  color: #ffffff;
  border: 1px solid #00FF7F;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(0, 255, 127, 1);
  transform: translateY(-2px);
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.data-card {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
}

.data-card h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  font-size: 1.1rem;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 8px;
}

.data-content {
  font-size: 0.9rem;
}

.no-data {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.kpi-item,
.month-item,
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(30, 144, 255, 0.1);
}

.kpi-title,
.month-name,
.info-label {
  color: #7BDEFF;
}

.kpi-value,
.month-value,
.info-value {
  color: #00FF7F;
  font-weight: 600;
}

.warehouse-list {
  margin-top: 10px;
  max-height: 150px;
  overflow-y: auto;
}

.warehouse-item {
  padding: 2px 0;
  font-size: 0.8rem;
  color: #7BDEFF;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.status-label {
  color: #7BDEFF;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.status-value {
  color: #00FF7F;
  font-weight: 600;
  font-size: 1rem;
}

.status-value.loading {
  color: #FFD700;
}

.status-value.error {
  color: #FF6B6B;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-grid,
  .data-grid,
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .region-buttons {
    justify-content: center;
  }
}
</style>
