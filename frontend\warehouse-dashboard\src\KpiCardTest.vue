<template>
  <div class="test-page">
    <AppHeader title="KPI 卡片组件测试" />
    
    <div class="test-container">
      <div class="section">
        <h2>核心 KPI 指标展示</h2>
        <div class="kpi-grid">
          <!-- TOC总单量 -->
          <KpiCard
            title="TOC总单量"
            :value="12580"
            unit="单"
            type="primary"
            icon="📦"
            :trend="5.2"
            trend-label="较昨日"
            description="今日TOC业务总订单量"
          />
          
          <!-- TOB总单量 -->
          <KpiCard
            title="TOB总单量"
            :value="8960"
            unit="单"
            type="success"
            icon="🏢"
            :trend="-2.1"
            trend-label="较昨日"
            description="今日TOB业务总订单量"
          />
          
          <!-- 运输成本 -->
          <KpiCard
            title="运输成本"
            :value="156.8"
            unit="万元"
            type="warning"
            icon="🚛"
            :trend="1.8"
            trend-label="较昨日"
            :precision="1"
            description="当日运输总成本"
          />
          
          <!-- 客户投诉 -->
          <KpiCard
            title="客户投诉"
            :value="23"
            unit="件"
            type="danger"
            icon="⚠️"
            :trend="-15.3"
            trend-label="较昨日"
            description="当日客户投诉总数"
          />
        </div>
      </div>

      <div class="section">
        <h2>不同类型和状态</h2>
        <div class="kpi-grid">
          <!-- 大数值展示 -->
          <KpiCard
            title="总处理量"
            :value="1256789"
            unit="件"
            type="info"
            icon="📊"
            :trend="8.5"
            description="累计处理包裹数量"
          />
          
          <!-- 百分比展示 -->
          <KpiCard
            title="仓储利用率"
            :value="78.5"
            unit="%"
            type="success"
            icon="🏭"
            :trend="2.3"
            :precision="1"
            description="当前仓库空间利用率"
          />
          
          <!-- 加载状态 -->
          <KpiCard
            title="实时数据"
            :value="0"
            unit="单"
            type="primary"
            icon="⏱️"
            :loading="true"
            description="数据加载中..."
          />
          
          <!-- 无趋势数据 -->
          <KpiCard
            title="系统状态"
            value="正常"
            type="success"
            icon="✅"
            description="系统运行状态良好"
            :thousand-separator="false"
          />
        </div>
      </div>

      <div class="section">
        <h2>自定义底部内容</h2>
        <div class="kpi-grid">
          <KpiCard
            title="订单完成率"
            :value="96.8"
            unit="%"
            type="success"
            icon="✅"
            :trend="1.2"
            :precision="1"
          >
            <template #footer>
              <div class="custom-footer">
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 96.8%"></div>
                </div>
                <div class="progress-text">目标: 95%</div>
              </div>
            </template>
          </KpiCard>
          
          <KpiCard
            title="平均响应时间"
            :value="2.3"
            unit="秒"
            type="info"
            icon="⚡"
            :trend="-5.8"
            :precision="1"
          >
            <template #footer>
              <div class="custom-footer">
                <div class="status-indicators">
                  <span class="status-dot good"></span>
                  <span class="status-text">响应良好</span>
                </div>
              </div>
            </template>
          </KpiCard>
        </div>
      </div>

      <div class="section">
        <h2>实时数据模拟</h2>
        <div class="kpi-grid">
          <KpiCard
            title="实时订单"
            :value="realtimeOrders"
            unit="单"
            type="primary"
            icon="📈"
            :trend="realtimeTrend"
            trend-label="实时变化"
            description="每5秒更新一次"
          />
        </div>
        
        <div class="controls">
          <button @click="toggleRealtime" class="control-btn">
            {{ isRealtimeActive ? '停止' : '开始' }}实时更新
          </button>
          <button @click="simulateLoading" class="control-btn">
            模拟加载状态
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import AppHeader from './components/AppHeader.vue'
import KpiCard from './components/KpiCard.vue'

// 实时数据
const realtimeOrders = ref(1250)
const realtimeTrend = ref(0)
const isRealtimeActive = ref(false)
const realtimeInterval = ref(null)

// 实时数据更新
const updateRealtimeData = () => {
  const change = Math.floor(Math.random() * 20) - 10 // -10 到 +10
  realtimeOrders.value += change
  realtimeTrend.value = (change / realtimeOrders.value * 100).toFixed(1)
}

// 切换实时更新
const toggleRealtime = () => {
  if (isRealtimeActive.value) {
    clearInterval(realtimeInterval.value)
    isRealtimeActive.value = false
  } else {
    realtimeInterval.value = setInterval(updateRealtimeData, 5000)
    isRealtimeActive.value = true
  }
}

// 模拟加载状态
const simulateLoading = () => {
  // 这里可以添加加载状态的演示逻辑
  console.log('模拟加载状态')
}

onMounted(() => {
  console.log('KPI Card 测试页面已加载')
})

onUnmounted(() => {
  if (realtimeInterval.value) {
    clearInterval(realtimeInterval.value)
  }
})
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.control-btn {
  background: rgba(30, 144, 255, 0.8);
  color: #ffffff;
  border: 1px solid #1E90FF;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(30, 144, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 144, 255, 0.3);
}

/* 自定义底部内容样式 */
.custom-footer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00FF7F, #32CD32);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #7BDEFF;
  text-align: center;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.good {
  background: #00FF7F;
  box-shadow: 0 0 6px rgba(0, 255, 127, 0.5);
}

.status-text {
  font-size: 0.75rem;
  color: #7BDEFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }
  
  .controls {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .kpi-grid {
    grid-template-columns: 1fr;
  }
}
</style>
