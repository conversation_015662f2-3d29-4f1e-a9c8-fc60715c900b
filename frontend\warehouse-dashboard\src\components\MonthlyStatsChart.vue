<template>
  <div class="monthly-stats-container" ref="chartContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

interface MonthlyData {
  month: string;
  orders: number;
  revenue: number;
  efficiency: number;
}

interface Props {
  data?: MonthlyData[];
  height?: string;
  theme?: 'dark' | 'light';
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  height: '100%',
  theme: 'dark'
});

// Refs
const chartContainer = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 默认数据
const defaultData: MonthlyData[] = [
  { month: '1月', orders: 1200, revenue: 2400, efficiency: 85 },
  { month: '2月', orders: 1100, revenue: 2200, efficiency: 82 },
  { month: '3月', orders: 1350, revenue: 2700, efficiency: 88 },
  { month: '4月', orders: 1450, revenue: 2900, efficiency: 90 },
  { month: '5月', orders: 1600, revenue: 3200, efficiency: 92 },
  { month: '6月', orders: 1750, revenue: 3500, efficiency: 94 },
  { month: '7月', orders: 1900, revenue: 3800, efficiency: 96 },
  { month: '8月', orders: 2100, revenue: 4200, efficiency: 98 },
  { month: '9月', orders: 2250, revenue: 4500, efficiency: 97 },
  { month: '10月', orders: 2400, revenue: 4800, efficiency: 99 },
  { month: '11月', orders: 2300, revenue: 4600, efficiency: 98 },
  { month: '12月', orders: 2500, revenue: 5000, efficiency: 100 }
];

// 图表配置
const getChartOption = () => {
  const chartData = props.data.length > 0 ? props.data : defaultData;
  
  return {
    backgroundColor: 'transparent',
    title: {
      text: '月度运营趋势',
      left: 'center',
      top: '3%',
      textStyle: {
        color: '#7BDEFF',
        fontSize: 14,
        fontWeight: 'bold',
        textShadowColor: 'rgba(123, 222, 255, 0.5)',
        textShadowBlur: 8
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(6, 22, 74, 0.95)',
      borderColor: '#00D4FF',
      borderWidth: 2,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      extraCssText: 'box-shadow: 0 0 20px rgba(0, 212, 255, 0.5); border-radius: 8px;',
      formatter: function(params: any) {
        let result = `<div style="padding: 8px;">`;
        result += `<div style="color: #7BDEFF; font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid rgba(123, 222, 255, 0.3); padding-bottom: 4px;">${params[0].axisValue}</div>`;
        
        params.forEach((param: any) => {
          const color = param.color;
          let unit = '';
          let value = param.value;
          
          if (param.seriesName === '订单量') {
            unit = '单';
          } else if (param.seriesName === '营收') {
            unit = '万元';
            value = (value / 100).toFixed(1);
          } else if (param.seriesName === '效率') {
            unit = '%';
          }
          
          result += `<div style="margin: 4px 0; display: flex; align-items: center;">`;
          result += `<span style="display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;"></span>`;
          result += `<span style="color: #A0D8EF; margin-right: 8px;">${param.seriesName}:</span>`;
          result += `<span style="color: ${color}; font-weight: bold;">${value}${unit}</span>`;
          result += `</div>`;
        });
        
        result += `</div>`;
        return result;
      }
    },
    legend: {
      data: ['订单量', '营收', '效率'],
      top: '12%',
      textStyle: {
        color: '#7BDEFF',
        fontSize: 11
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 20
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '25%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.month),
      axisLine: {
        lineStyle: {
          color: 'rgba(123, 222, 255, 0.3)',
          width: 1
        }
      },
      axisTick: {
        lineStyle: {
          color: 'rgba(123, 222, 255, 0.3)'
        }
      },
      axisLabel: {
        color: '#A0D8EF',
        fontSize: 10,
        margin: 8
      },
      splitLine: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '订单量/营收',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: 'rgba(123, 222, 255, 0.3)'
          }
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(123, 222, 255, 0.3)'
          }
        },
        axisLabel: {
          color: '#A0D8EF',
          fontSize: 10,
          formatter: function(value: number) {
            if (value >= 1000) {
              return (value / 1000).toFixed(1) + 'k';
            }
            return value.toString();
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(123, 222, 255, 0.1)',
            type: 'dashed'
          }
        },
        nameTextStyle: {
          color: '#7BDEFF',
          fontSize: 10
        }
      },
      {
        type: 'value',
        name: '效率(%)',
        position: 'right',
        min: 80,
        max: 100,
        axisLine: {
          lineStyle: {
            color: 'rgba(123, 222, 255, 0.3)'
          }
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(123, 222, 255, 0.3)'
          }
        },
        axisLabel: {
          color: '#A0D8EF',
          fontSize: 10,
          formatter: '{value}%'
        },
        splitLine: {
          show: false
        },
        nameTextStyle: {
          color: '#7BDEFF',
          fontSize: 10
        }
      }
    ],
    series: [
      {
        name: '订单量',
        type: 'bar',
        data: chartData.map(item => item.orders),
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#00D4FF' },
              { offset: 1, color: 'rgba(0, 212, 255, 0.3)' }
            ]
          },
          borderRadius: [4, 4, 0, 0],
          shadowBlur: 8,
          shadowColor: 'rgba(0, 212, 255, 0.4)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 212, 255, 0.8)'
          }
        },
        barWidth: '25%',
        animationDelay: function (idx: number) {
          return idx * 100;
        }
      },
      {
        name: '营收',
        type: 'bar',
        data: chartData.map(item => item.revenue),
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#4ECDC4' },
              { offset: 1, color: 'rgba(78, 205, 196, 0.3)' }
            ]
          },
          borderRadius: [4, 4, 0, 0],
          shadowBlur: 8,
          shadowColor: 'rgba(78, 205, 196, 0.4)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(78, 205, 196, 0.8)'
          }
        },
        barWidth: '25%',
        animationDelay: function (idx: number) {
          return idx * 100 + 50;
        }
      },
      {
        name: '效率',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.map(item => item.efficiency),
        lineStyle: {
          color: '#00FF88',
          width: 3,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 255, 136, 0.4)'
        },
        itemStyle: {
          color: '#00FF88',
          borderWidth: 2,
          borderColor: '#ffffff',
          shadowBlur: 8,
          shadowColor: 'rgba(0, 255, 136, 0.6)'
        },
        symbol: 'circle',
        symbolSize: 8,
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 255, 136, 0.8)',
            borderWidth: 3
          },
          lineStyle: {
            width: 4
          }
        },
        smooth: true,
        animationDelay: function (idx: number) {
          return idx * 100 + 100;
        }
      }
    ],
    animationEasing: 'cubicOut',
    animationDuration: 1000
  };
};

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  chartInstance = echarts.init(chartContainer.value, props.theme);
  chartInstance.setOption(getChartOption());

  // 响应式处理
  const resizeHandler = () => {
    chartInstance?.resize();
  };
  window.addEventListener('resize', resizeHandler);

  return () => {
    window.removeEventListener('resize', resizeHandler);
  };
};

// 更新图表数据
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getChartOption());
  }
};

// 生命周期
onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 监听数据变化
watch(() => props.data, updateChart, { deep: true });
</script>

<style scoped>
.monthly-stats-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
  position: relative;
  background: radial-gradient(circle at center, rgba(0, 212, 255, 0.03) 0%, transparent 70%);
  border-radius: 8px;
}
</style>
