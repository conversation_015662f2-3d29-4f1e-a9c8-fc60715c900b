import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 新增：用于交叉筛选的月度销售明细数据
export const detailedSalesData = {
  // 默认的全国视图
  '全国': [
    { x: '1月', y: 2100 },
    { x: '2月', y: 1800 },
    { x: '3月', y: 2500 },
    { x: '4月', y: 3200 },
    { x: '5月', y: 3000 },
    { x: '6月', y: 2800 },
  ],
  // 各省的明细数据
  '广东': [
    { x: '1月', y: 350 }, { x: '2月', y: 300 }, { x: '3月', y: 400 },
    { x: '4月', y: 550 }, { x: '5月', y: 500 }, { x: '6月', y: 450 },
  ],
  '浙江': [
    { x: '1月', y: 250 }, { x: '2月', y: 220 }, { x: '3月', y: 310 },
    { x: '4月', y: 400 }, { x: '5月', y: 380 }, { x: '6月', y: 350 },
  ],
  '江苏': [
    { x: '1月', y: 280 }, { x: '2月', y: 240 }, { x: '3月', y: 330 },
    { x: '4月', y: 420 }, { x: '5月', y: 400 }, { x: '6月', y: 370 },
  ],
  '上海': [
    { x: '1月', y: 180 }, { x: '2月', y: 160 }, { x: '3月', y: 220 },
    { x: '4月', y: 280 }, { x: '5月', y: 260 }, { x: '6月', y: 240 },
  ],
  '北京': [
    { x: '1月', y: 200 }, { x: '2月', y: 180 }, { x: '3月', y: 250 },
    { x: '4月', y: 320 }, { x: '5月', y: 300 }, { x: '6月', y: 280 },
  ],
  '山东': [
    { x: '1月', y: 220 }, { x: '2月', y: 200 }, { x: '3月', y: 270 },
    { x: '4月', y: 340 }, { x: '5月', y: 320 }, { x: '6月', y: 300 },
  ],
  '四川': [
    { x: '1月', y: 150 }, { x: '2月', y: 130 }, { x: '3月', y: 180 },
    { x: '4月', y: 230 }, { x: '5月', y: 210 }, { x: '6月', y: 190 },
  ],
  '河南': [
    { x: '1月', y: 140 }, { x: '2月', y: 120 }, { x: '3月', y: 170 },
    { x: '4月', y: 220 }, { x: '5月', y: 200 }, { x: '6月', y: 180 },
  ],
  '湖北': [
    { x: '1月', y: 160 }, { x: '2月', y: 140 }, { x: '3月', y: 190 },
    { x: '4月', y: 240 }, { x: '5月', y: 220 }, { x: '6月', y: 200 },
  ],
  '湖南': [
    { x: '1月', y: 155 }, { x: '2月', y: 135 }, { x: '3月', y: 185 },
    { x: '4月', y: 235 }, { x: '5月', y: 215 }, { x: '6月', y: 195 },
  ]
}

// 数据项接口定义
export interface DataItem {
  name: string
  value: number
  [key: string]: any
}

// 数据源接口定义
export interface DataSource {
  id: string
  name: string
  description?: string
  type?: 'chart' | 'table' | 'metric'
}

// 模拟的后端数据库
const MOCK_DB: Record<string, DataItem[]> = {
  'sales-q1': [
    { name: '笔记本', value: 4820, category: '电脑设备' },
    { name: '显示器', value: 3109, category: '电脑设备' },
    { name: '键盘', value: 1523, category: '外设' },
    { name: '鼠标', value: 987, category: '外设' },
    { name: '音响', value: 756, category: '外设' },
  ],
  'sales-q2': [
    { name: '笔记本', value: 5120, category: '电脑设备' },
    { name: '显示器', value: 3500, category: '电脑设备' },
    { name: '风扇', value: 2100, category: '散热设备' },
    { name: '键盘', value: 1321, category: '外设' },
    { name: '耳机', value: 890, category: '外设' },
  ],
  'user-growth': [
    { name: '一月', value: 120, growth: 15.2 },
    { name: '二月', value: 250, growth: 108.3 },
    { name: '三月', value: 180, growth: -28.0 },
    { name: '四月', value: 320, growth: 77.8 },
    { name: '五月', value: 280, growth: -12.5 },
    { name: '六月', value: 450, growth: 60.7 },
  ],
  'revenue-monthly': [
    { name: '1月', value: 85000, target: 80000 },
    { name: '2月', value: 92000, target: 85000 },
    { name: '3月', value: 78000, target: 90000 },
    { name: '4月', value: 105000, target: 95000 },
    { name: '5月', value: 98000, target: 100000 },
    { name: '6月', value: 112000, target: 105000 },
  ],
  'region-distribution': [
    { name: '华东', value: 35.6, count: 1250 },
    { name: '华南', value: 28.3, count: 980 },
    { name: '华北', value: 18.7, count: 650 },
    { name: '西南', value: 10.2, count: 360 },
    { name: '东北', value: 4.8, count: 170 },
    { name: '西北', value: 2.4, count: 85 },
  ],
  'product-performance': [
    { name: '产品A', value: 95.2, satisfaction: 4.8, sales: 2340 },
    { name: '产品B', value: 87.6, satisfaction: 4.5, sales: 1890 },
    { name: '产品C', value: 92.1, satisfaction: 4.7, sales: 2100 },
    { name: '产品D', value: 78.9, satisfaction: 4.2, sales: 1560 },
    { name: '产品E', value: 89.3, satisfaction: 4.6, sales: 1780 },
  ],
  'province-sales': [
    { name: '上海', value: 4820 }, { name: '北京', value: 3980 },
    { name: '广东', value: 3500 }, { name: '江苏', value: 3100 },
    { name: '浙江', value: 2900 }, { name: '山东', value: 2600 },
    { name: '四川', value: 2200 }, { name: '河南', value: 1800 },
    { name: '湖北', value: 1750 }, { name: '湖南', value: 1700 },
    { name: '福建', value: 1500 }, { name: '安徽', value: 1400 },
    { name: '陕西', value: 1300 }, { name: '辽宁', value: 1100 },
    { name: '重庆', value: 900 },  { name: '新疆', value: 500 },
  ],
  // 新增按省份聚合的销售趋势数据
  'salesByProvince': [
    { province: '广东', data: [ { x: '1月', y: 120 }, { x: '2月', y: 200 }, { x: '3月', y: 150 }, { x: '4月', y: 80 }, { x: '5月', y: 70 }, { x: '6月', y: 110 } ] },
    { province: '江苏', data: [ { x: '1月', y: 90 }, { x: '2月', y: 130 }, { x: '3月', y: 100 }, { x: '4月', y: 60 }, { x: '5月', y: 50 }, { x: '6月', y: 90 } ] },
    { province: '山东', data: [ { x: '1月', y: 80 }, { x: '2月', y: 110 }, { x: '3月', y: 90 }, { x: '4月', y: 70 }, { x: '5月', y: 60 }, { x: '6月', y: 80 } ] },
    { province: '浙江', data: [ { x: '1月', y: 110 }, { x: '2月', y: 150 }, { x: '3月', y: 130 }, { x: '4月', y: 90 }, { x: '5月', y: 80 }, { x: '6月', y: 100 } ] },
    { province: '上海', data: [ { x: '1月', y: 140 }, { x: '2月', y: 180 }, { x: '3月', y: 160 }, { x: '4月', y: 100 }, { x: '5月', y: 90 }, { x: '6月', y: 120 } ] },
    { province: '北京', data: [ { x: '1月', y: 130 }, { x: '2月', y: 170 }, { x: '3月', y: 140 }, { x: '4月', y: 95 }, { x: '5月', y: 85 }, { x: '6月', y: 115 } ] },
    { province: '四川', data: [ { x: '1月', y: 70 }, { x: '2月', y: 95 }, { x: '3月', y: 80 }, { x: '4月', y: 55 }, { x: '5月', y: 45 }, { x: '6月', y: 65 } ] },
    { province: '河南', data: [ { x: '1月', y: 60 }, { x: '2月', y: 85 }, { x: '3月', y: 70 }, { x: '4月', y: 50 }, { x: '5月', y: 40 }, { x: '6月', y: 55 } ] },
    { province: '湖北', data: [ { x: '1月', y: 65 }, { x: '2月', y: 88 }, { x: '3月', y: 75 }, { x: '4月', y: 52 }, { x: '5月', y: 42 }, { x: '6月', y: 58 } ] },
    { province: '湖南', data: [ { x: '1月', y: 62 }, { x: '2月', y: 86 }, { x: '3月', y: 72 }, { x: '4月', y: 48 }, { x: '5月', y: 38 }, { x: '6月', y: 56 } ] }
  ],
  // 新增：模拟"年度总收入"数据
  'revenue': [
    { x: '第一季度', y: 1500 },
    { x: '第二季度', y: 2300 },
    { x: '第三季度', y: 2800 },
    { x: '第四季度', y: 4200 },
  ],
  // 新增：模拟"用户增长"数据
  'userGrowth': [
    { x: '1月', y: 50 },
    { x: '2月', y: 80 },
    { x: '3月', y: 120 },
    { x: '4月', y: 250 },
    { x: '5月', y: 400 },
    { x: '6月', y: 650 },
  ],
  // 新增：月度销售明细数据（支持筛选）
  'monthlySales': detailedSalesData
}

export const useDataSourceStore = defineStore('dataSource', () => {
  // 可用数据源列表
  const sources = ref<Record<string, DataSource>>({
    'sales-q1': { 
      id: 'sales-q1', 
      name: 'Q1 销售数据', 
      description: '第一季度产品销售统计',
      type: 'chart'
    },
    'sales-q2': { 
      id: 'sales-q2', 
      name: 'Q2 销售数据', 
      description: '第二季度产品销售统计',
      type: 'chart'
    },
    'user-growth': { 
      id: 'user-growth', 
      name: '用户增长曲线', 
      description: '月度用户增长趋势分析',
      type: 'chart'
    },
    'revenue-monthly': { 
      id: 'revenue-monthly', 
      name: '月度营收数据', 
      description: '每月营收与目标对比',
      type: 'chart'
    },
    'region-distribution': { 
      id: 'region-distribution', 
      name: '区域分布统计', 
      description: '各地区业务分布情况',
      type: 'chart'
    },
    'product-performance': {
      id: 'product-performance',
      name: '产品性能指标',
      description: '产品综合表现评估',
      type: 'metric'
    },
    'province-sales': {
      id: 'province-sales',
      name: '各省销售数据',
      description: '全国各省份销售业绩分布',
      type: 'chart'
    },
    'salesByProvince': {
      id: 'salesByProvince',
      name: '省份销售趋势',
      description: '各省份月度销售趋势数据',
      type: 'chart'
    },
    'revenue': {
      id: 'revenue',
      name: '季度总收入',
      description: '年度各季度总收入数据',
      type: 'chart'
    },
    'userGrowth': {
      id: 'userGrowth',
      name: '用户增长曲线',
      description: '月度用户增长趋势数据',
      type: 'chart'
    },
    'monthlySales': {
      id: 'monthlySales',
      name: '月度销售明细',
      description: '支持省份筛选的月度销售数据',
      type: 'chart'
    }
  })

  // 加载状态管理
  const loadingStates = ref<Record<string, boolean>>({})

  // 获取图表数据源选项列表（专门给图表配置使用）
  const getChartDataSourceOptions = computed(() => {
    return [
      { value: 'monthlySales', label: '月度销售明细' },
      { value: 'salesByProvince', label: '省级销售数据' },
      { value: 'revenue', label: '季度总收入' },
      { value: 'userGrowth', label: '用户增长曲线' },
      { value: 'sales-q1', label: 'Q1 销售数据' },
      { value: 'sales-q2', label: 'Q2 销售数据' },
      { value: 'user-growth', label: '用户增长统计' },
      { value: 'revenue-monthly', label: '月度营收数据' },
      { value: 'region-distribution', label: '区域分布统计' },
      { value: 'product-performance', label: '产品性能指标' }
    ];
  });

  // 模拟 API 调用 - 重要修改：让 fetchData 接受一个 filter 对象
  const fetchData = async (sourceId: string, filter: any = {}): Promise<any> => {
    if (!sourceId || !MOCK_DB[sourceId]) {
      console.warn(`Data source not found: ${sourceId}`)
      return []
    }

    console.log(`[DataSource] 正在获取数据 (ID: ${sourceId}, Filter: ${JSON.stringify(filter)})`)

    // 设置加载状态
    loadingStates.value[sourceId] = true

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 300))

      let data = MOCK_DB[sourceId] || []

      // 为 monthlySales 数据源添加过滤逻辑
      if (sourceId === 'monthlySales') {
        // 如果提供了 filter 且数据中存在该 filter 的键
        if (filter && data[filter]) {
          console.log(`[DataSource] 已筛选省份: ${filter}`, data[filter])
          return data[filter] // 返回该省份的明细数据
        }
        console.log(`[DataSource] 返回全国数据`, data['全国'])
        return data['全国'] // 否则返回全国总览数据
      }

      // 如果有省份筛选，并且是针对 salesByProvince 的请求
      if (filter.province && sourceId === 'salesByProvince') {
        const filteredData = data.filter((item: any) => item.province === filter.province)
        // 如果找到了这个省份的数据，就返回这个省份的数据系列
        if (filteredData.length > 0) {
          console.log(`[DataSource] 已筛选省份: ${filter.province}`, filteredData[0].data)
          return filteredData[0].data
        } else {
          // 如果没找到，返回一个空数组，图表会显示"暂无数据"
          console.log(`[DataSource] 未找到省份数据: ${filter.province}`)
          return []
        }
      }

      // 如果没有筛选条件或不是相关数据源，返回原始数据
      console.log(`Data fetched successfully for ${sourceId}:`, data)
      return [...data] // 返回数据的副本
    } catch (error) {
      console.error(`Error fetching data for ${sourceId}:`, error)
      throw error
    } finally {
      // 清除加载状态
      loadingStates.value[sourceId] = false
    }
  }

  // 获取数据源信息
  const getDataSource = (sourceId: string): DataSource | null => {
    return sources.value[sourceId] || null
  }

  // 获取所有数据源列表
  const getAllDataSources = (): DataSource[] => {
    return Object.values(sources.value)
  }

  // 检查数据源是否存在
  const hasDataSource = (sourceId: string): boolean => {
    return sourceId in sources.value
  }

  // 获取加载状态
  const isLoading = (sourceId: string): boolean => {
    return loadingStates.value[sourceId] || false
  }

  // 预加载数据（可选功能）
  const preloadData = async (sourceIds: string[]): Promise<void> => {
    const promises = sourceIds
      .filter(id => hasDataSource(id))
      .map(id => fetchData(id).catch(err => {
        console.warn(`Failed to preload data for ${id}:`, err)
        return []
      }))
    
    await Promise.all(promises)
    console.log('Data preloading completed')
  }

  return {
    sources,
    loadingStates,
    fetchData,
    getDataSource,
    getAllDataSources,
    hasDataSource,
    isLoading,
    preloadData,
    getChartDataSourceOptions
  }
})
