# 第四阶段功能测试指南 - 数据驱动系统

## 🎯 测试目标
验证数据驱动系统的完整功能：数据源管理、动态数据加载、组件响应式更新。

## 🧪 详细测试步骤

### 1. 数据源系统验证
1. 打开浏览器访问 http://localhost:5173/
2. 点击"动态仪表盘"按钮
3. 验证系统基础功能正常

### 2. 配置数据源功能测试

#### 2.1 数据源选择器验证
1. **添加组件并配置**：
   - 从工具箱拖拽一个"销售图表卡片"到画布
   - 点击组件的齿轮（⚙️）按钮
   - 验证配置窗口显示：
     - ✅ "数据源"下拉选择器
     - ✅ 默认显示"-- 请选择数据源 --"
     - ✅ 包含6个可选数据源

2. **数据源选项验证**：
   - 验证下拉菜单包含以下选项：
     - ✅ Q1 销售数据
     - ✅ Q2 销售数据  
     - ✅ 用户增长曲线
     - ✅ 月度营收数据
     - ✅ 区域分布统计
     - ✅ 产品性能指标

3. **预览功能验证**：
   - 选择"Q1 销售数据"
   - 验证预览区域显示：
     - ✅ 数据源名称更新
     - ✅ 显示"✓ 组件将显示来自..."提示
   - 未选择数据源时显示：
     - ✅ "⚠ 未选择数据源，组件将显示默认数据"

#### 2.2 数据源配置保存测试
1. **Q1数据源配置**：
   - 选择"Q1 销售数据"
   - 修改标题为"第一季度销售"
   - 点击保存
   - 验证：
     - ✅ 组件标题更新
     - ✅ 图表显示Q1数据（笔记本、显示器、键盘、鼠标、音响）
     - ✅ 显示加载动画

2. **Q2数据源配置**：
   - 添加另一个销售图表组件
   - 配置数据源为"Q2 销售数据"
   - 修改标题为"第二季度销售"
   - 验证：
     - ✅ 显示不同的数据（笔记本、显示器、风扇、键盘、耳机）
     - ✅ 两个组件显示不同内容

### 3. 动态数据加载测试

#### 3.1 加载状态验证
1. **加载动画测试**：
   - 配置组件数据源
   - 观察加载过程：
     - ✅ 显示"加载数据中..."文字
     - ✅ 有旋转加载图标
     - ✅ 加载完成后图表正常显示

2. **错误处理测试**：
   - 由于有5%的模拟错误概率，可能遇到加载失败
   - 验证错误处理：
     - ✅ 加载失败时使用默认数据
     - ✅ 控制台显示错误信息
     - ✅ 组件仍能正常显示

#### 3.2 数据响应式更新测试
1. **实时数据源切换**：
   - 打开组件配置
   - 从"Q1 销售数据"切换到"用户增长曲线"
   - 验证：
     - ✅ 图表立即更新为新数据
     - ✅ 显示月份数据（一月、二月、三月等）
     - ✅ 无需刷新页面

2. **标题同步更新**：
   - 修改组件标题
   - 验证图表标题实时更新

### 4. 多组件数据源测试

#### 4.1 不同组件类型测试
1. **基本信息卡片**：
   - 添加BasicInfoCard组件
   - 配置数据源为"产品性能指标"
   - 验证：
     - ✅ 数据根据选择的源动态计算
     - ✅ 总订单数、今日新增等数据更新

2. **关键指标卡片**：
   - 添加KeyMetricCard组件
   - 配置数据源为"用户增长曲线"
   - 验证：
     - ✅ 当前值显示数据源的第一个值
     - ✅ 趋势图根据数据动态生成
     - ✅ 同比环比数据计算

#### 4.2 多实例独立性测试
1. **创建多个相同组件**：
   - 添加3个SalesChartCard组件
   - 分别配置不同数据源：
     - 组件1：Q1 销售数据
     - 组件2：Q2 销售数据  
     - 组件3：区域分布统计
   - 验证：
     - ✅ 每个组件显示独立的数据
     - ✅ 配置互不干扰
     - ✅ 数据更新独立进行

### 5. 数据持久化测试

#### 5.1 配置保存验证
1. **完整配置保存**：
   - 配置多个组件的数据源
   - 刷新页面
   - 验证：
     - ✅ 所有数据源配置保留
     - ✅ 组件重新加载正确数据
     - ✅ 标题和尺寸配置保持

2. **localStorage数据检查**：
   - 打开浏览器开发者工具
   - 查看Application > Local Storage
   - 验证：
     - ✅ dashboardLayout包含dataSourceId字段
     - ✅ 数据结构完整

### 6. 边界情况测试

#### 6.1 无数据源状态
1. **默认数据显示**：
   - 添加组件但不配置数据源
   - 验证：
     - ✅ 显示默认静态数据
     - ✅ 组件功能正常
     - ✅ 配置提示显示警告

2. **数据源清空**：
   - 配置数据源后再选择"-- 请选择数据源 --"
   - 验证：
     - ✅ 组件恢复默认数据
     - ✅ 配置状态正确更新

#### 6.2 数据异常处理
1. **空数据处理**：
   - 模拟数据源返回空数组
   - 验证：
     - ✅ 图表显示"暂无数据"
     - ✅ 不会崩溃或报错

2. **网络错误处理**：
   - 由于5%错误概率，可能遇到网络错误
   - 验证：
     - ✅ 错误时回退到默认数据
     - ✅ 用户体验不受影响

### 7. 性能测试

#### 7.1 加载性能
1. **数据加载时间**：
   - 测量数据源切换响应时间
   - 预期：< 500ms（包含300ms模拟延迟）

2. **组件渲染性能**：
   - 添加多个组件同时配置数据源
   - 验证：
     - ✅ 页面响应流畅
     - ✅ 无明显卡顿

#### 7.2 内存使用
1. **组件清理**：
   - 添加和删除多个组件
   - 验证：
     - ✅ 删除组件时正确清理资源
     - ✅ 无内存泄漏

## 🔍 预期结果

### 成功标准
- [ ] 数据源选择器正常工作
- [ ] 组件能根据数据源显示不同数据
- [ ] 数据加载有适当的loading状态
- [ ] 配置变化能实时反映到组件
- [ ] 多组件数据源配置独立
- [ ] 所有配置能正确持久化
- [ ] 错误情况有合理的降级处理

### 性能指标
- 数据源切换响应时间 < 500ms
- 组件渲染时间 < 300ms
- 配置保存时间 < 100ms
- 页面加载时间 < 2s

## 🐛 常见问题排查

### 问题1：数据源选择器不显示
**可能原因**：
- dataSourceStore未正确导入
- 组件props未正确传递
- 配置模态框渲染问题

**解决方法**：
- 检查import语句
- 验证store状态
- 查看浏览器控制台错误

### 问题2：数据不更新
**可能原因**：
- watch监听器未触发
- 数据获取失败
- 组件未正确响应props变化

**解决方法**：
- 检查watch配置
- 查看网络请求
- 验证props传递

### 问题3：配置不保存
**可能原因**：
- dataSourceId未包含在保存逻辑中
- localStorage写入失败
- 序列化问题

**解决方法**：
- 检查updateWidgetProps函数
- 验证localStorage权限
- 查看数据序列化过程

## 📊 测试报告模板

```
测试日期：____
测试人员：____
浏览器版本：____

功能测试结果：
□ 数据源选择 - 通过/失败
□ 动态数据加载 - 通过/失败  
□ 组件响应更新 - 通过/失败
□ 多组件独立性 - 通过/失败
□ 配置持久化 - 通过/失败
□ 错误处理 - 通过/失败

性能测试结果：
□ 加载性能 - 通过/失败
□ 渲染性能 - 通过/失败
□ 内存使用 - 通过/失败

发现的问题：
1. ________________
2. ________________
3. ________________

总体评价：
□ 优秀 □ 良好 □ 一般 □ 需改进

建议：
________________
```

## 🚀 下一步增强功能

测试通过后，可以考虑以下增强：
1. 添加更多数据源类型
2. 实现数据源的CRUD操作
3. 添加数据刷新和缓存机制
4. 支持实时数据流
5. 实现数据源的权限控制
