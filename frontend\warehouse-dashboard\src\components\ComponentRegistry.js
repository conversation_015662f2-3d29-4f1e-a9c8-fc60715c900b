/**
 * 物流大数据展示平台 - 组件注册表
 * 用于验证组件完整性和管理组件依赖
 */

// 导入所有组件
import AppHeader from './AppHeader.vue'
import PieChart from './PieChart.vue'
import OverallKpiCard from './OverallKpiCard.vue'
import DailyOpsTrendChart from './DailyOpsTrendChart.vue'
import WarehouseStatusGauge from './WarehouseStatusGauge.vue'
import InventoryHealthPanel from './InventoryHealthPanel.vue'
import LaborCostPieChart from './LaborCostPieChart.vue'
import MonthlyKpiScoreboard from './MonthlyKpiScoreboard.vue'
import PersonnelEfficiencyTable from './PersonnelEfficiencyTable.vue'
import ServiceQualityTracker from './ServiceQualityTracker.vue'

// 组件清单配置
export const COMPONENT_REGISTRY = {
  // 基础组件
  'AppHeader': {
    component: AppHeader,
    category: '基础组件',
    description: '应用头部组件，包含标题和实时时间',
    props: ['title'],
    dataSource: '无',
    status: 'completed'
  },
  
  'PieChart': {
    component: PieChart,
    category: '图表组件',
    description: '通用饼图组件',
    props: ['title', 'chartId', 'data'],
    dataSource: '通用',
    status: 'completed'
  },

  // 核心业务组件
  'OverallKpiCard': {
    component: OverallKpiCard,
    category: '核心指标',
    description: '顶层核心指标展示：TOC/TOB总单量、运输成本、客户投诉数',
    props: ['title', 'data'],
    dataSource: '日常运营数据表',
    chartType: '数字翻牌器 (KPI Card)',
    status: 'completed'
  },

  'DailyOpsTrendChart': {
    component: DailyOpsTrendChart,
    category: '趋势分析',
    description: '过去30天核心运营指标变化趋势：TOC单量、TOB单量、入库方数',
    props: ['title', 'chartId', 'data'],
    dataSource: '日常运营数据表',
    chartType: '折线/柱状混合图 (Line/Bar)',
    status: 'completed'
  },

  'WarehouseStatusGauge': {
    component: WarehouseStatusGauge,
    category: '状态监控',
    description: '实时展示仓储利用率和剩余总库容',
    props: ['title', 'chartId', 'data'],
    dataSource: '日常运营数据表',
    chartType: '仪表盘 (Gauge)',
    status: 'completed'
  },

  'InventoryHealthPanel': {
    component: InventoryHealthPanel,
    category: '库存管理',
    description: '库存健康度：库存周转率(日)和库存准确率(月)',
    props: ['title', 'data'],
    dataSource: '日常/月度数据表',
    chartType: '进度条/仪表盘 (Progress)',
    status: 'completed'
  },

  'LaborCostPieChart': {
    component: LaborCostPieChart,
    category: '人力成本',
    description: '人力成本结构：正式工总工时与劳务工总工时占比',
    props: ['title', 'chartId', 'data'],
    dataSource: '日常运营数据表',
    chartType: '环形饼图 (Donut Chart)',
    status: 'completed'
  },

  'MonthlyKpiScoreboard': {
    component: MonthlyKpiScoreboard,
    category: '绩效管理',
    description: '月度绩效看板：所有月度考核指标完成率',
    props: ['title', 'data'],
    dataSource: '月度考核指标表',
    chartType: '计分板/列表 (Scoreboard)',
    status: 'completed'
  },

  'PersonnelEfficiencyTable': {
    component: PersonnelEfficiencyTable,
    category: '人效分析',
    description: '人效对比分析：各仓月TOC/TOB人效环比/同比',
    props: ['title', 'data'],
    dataSource: '月度考核指标表',
    chartType: '表格/分组柱状图 (Table)',
    status: 'completed'
  },

  'ServiceQualityTracker': {
    component: ServiceQualityTracker,
    category: '服务质量',
    description: '服务质量跟踪：客户投诉数量和逆向物流总单量每日趋势',
    props: ['title', 'chartId', 'data'],
    dataSource: '日常运营数据表',
    chartType: '折线图 (Line Chart)',
    status: 'completed'
  }
}

// 按类别分组的组件
export const COMPONENTS_BY_CATEGORY = {
  '基础组件': ['AppHeader', 'PieChart'],
  '核心指标': ['OverallKpiCard'],
  '趋势分析': ['DailyOpsTrendChart'],
  '状态监控': ['WarehouseStatusGauge'],
  '库存管理': ['InventoryHealthPanel'],
  '人力成本': ['LaborCostPieChart'],
  '绩效管理': ['MonthlyKpiScoreboard'],
  '人效分析': ['PersonnelEfficiencyTable'],
  '服务质量': ['ServiceQualityTracker']
}

// 数据源映射
export const DATA_SOURCE_MAPPING = {
  '日常运营数据表': [
    'OverallKpiCard',
    'DailyOpsTrendChart', 
    'WarehouseStatusGauge',
    'LaborCostPieChart',
    'ServiceQualityTracker'
  ],
  '月度考核指标表': [
    'MonthlyKpiScoreboard',
    'PersonnelEfficiencyTable'
  ],
  '日常/月度数据表': [
    'InventoryHealthPanel'
  ]
}

// 验证函数
export function validateComponentRegistry() {
  const results = {
    totalComponents: Object.keys(COMPONENT_REGISTRY).length,
    completedComponents: 0,
    missingComponents: [],
    validationErrors: []
  }

  Object.entries(COMPONENT_REGISTRY).forEach(([name, config]) => {
    // 检查组件是否存在
    if (!config.component) {
      results.missingComponents.push(name)
      results.validationErrors.push(`组件 ${name} 未正确导入`)
    }

    // 检查必要配置
    if (!config.description) {
      results.validationErrors.push(`组件 ${name} 缺少描述`)
    }

    if (!config.dataSource) {
      results.validationErrors.push(`组件 ${name} 缺少数据源说明`)
    }

    if (config.status === 'completed') {
      results.completedComponents++
    }
  })

  return results
}

// 获取组件统计信息
export function getComponentStats() {
  const stats = {
    byCategory: {},
    byDataSource: {},
    byStatus: { completed: 0, pending: 0, error: 0 }
  }

  Object.entries(COMPONENT_REGISTRY).forEach(([name, config]) => {
    // 按类别统计
    if (!stats.byCategory[config.category]) {
      stats.byCategory[config.category] = 0
    }
    stats.byCategory[config.category]++

    // 按数据源统计
    if (!stats.byDataSource[config.dataSource]) {
      stats.byDataSource[config.dataSource] = 0
    }
    stats.byDataSource[config.dataSource]++

    // 按状态统计
    stats.byStatus[config.status || 'pending']++
  })

  return stats
}

// 导出所有组件
export {
  AppHeader,
  PieChart,
  OverallKpiCard,
  DailyOpsTrendChart,
  WarehouseStatusGauge,
  InventoryHealthPanel,
  LaborCostPieChart,
  MonthlyKpiScoreboard,
  PersonnelEfficiencyTable,
  ServiceQualityTracker
}

// 默认导出
export default COMPONENT_REGISTRY
