<template>
  <div class="basic-info-card h-full bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">📊</div>
    </div>
    
    <div class="space-y-3">
      <div class="flex justify-between items-center">
        <span class="text-blue-200">总订单数</span>
        <span class="text-xl font-bold">{{ formatNumber(totalOrders) }}</span>
      </div>
      
      <div class="flex justify-between items-center">
        <span class="text-blue-200">今日新增</span>
        <span class="text-xl font-bold text-green-300">+{{ formatNumber(todayOrders) }}</span>
      </div>
      
      <div class="flex justify-between items-center">
        <span class="text-blue-200">处理中</span>
        <span class="text-xl font-bold text-yellow-300">{{ formatNumber(processingOrders) }}</span>
      </div>
      
      <div class="flex justify-between items-center">
        <span class="text-blue-200">已完成</span>
        <span class="text-xl font-bold text-green-300">{{ formatNumber(completedOrders) }}</span>
      </div>
    </div>
    
    <div class="mt-4 pt-3 border-t border-blue-500">
      <div class="flex justify-between items-center text-sm">
        <span class="text-blue-200">完成率</span>
        <span class="font-semibold">{{ completionRate }}%</span>
      </div>
      <div class="w-full bg-blue-700 rounded-full h-2 mt-2">
        <div 
          class="bg-green-400 h-2 rounded-full transition-all duration-500"
          :style="{ width: `${completionRate}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import { useDataSourceStore, type DataItem } from '@/stores/dataSource'

interface Props {
  title?: string
  dataSourceId?: string | null
  widgetId?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '基本信息',
  dataSourceId: null,
  widgetId: undefined
})

const dataSourceStore = useDataSourceStore()

// 数据状态
const totalOrders = ref(12580)
const todayOrders = ref(156)
const processingOrders = ref(89)
const completedOrders = ref(11435)
const isLoading = ref(false)

// 更新数据的函数
const updateData = async () => {
  if (!props.dataSourceId) {
    // 使用默认数据
    totalOrders.value = 12580
    todayOrders.value = 156
    processingOrders.value = 89
    completedOrders.value = 11435
    return
  }

  try {
    isLoading.value = true
    const data = await dataSourceStore.fetchData(props.dataSourceId)

    if (data.length > 0) {
      // 根据数据源更新显示数据
      const firstItem = data[0]
      totalOrders.value = firstItem.value || 0
      todayOrders.value = Math.floor(firstItem.value * 0.1) || 0
      processingOrders.value = Math.floor(firstItem.value * 0.05) || 0
      completedOrders.value = Math.floor(firstItem.value * 0.85) || 0
    }
  } catch (error) {
    console.error('Failed to update basic info data:', error)
  } finally {
    isLoading.value = false
  }
}

// 计算完成率
const completionRate = computed(() => {
  if (totalOrders.value === 0) return 0
  return Math.round((completedOrders.value / totalOrders.value) * 100)
})

// 格式化数字
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

// 监听数据源变化
watch(() => [props.dataSourceId, props.title], () => {
  updateData()
}, { deep: true })

// 模拟数据更新
onMounted(() => {
  // 初始加载数据
  updateData()

  const interval = setInterval(() => {
    // 只有在没有选择数据源时才进行随机更新
    if (!props.dataSourceId && Math.random() > 0.7) {
      todayOrders.value += Math.floor(Math.random() * 3)
      totalOrders.value += Math.floor(Math.random() * 3)

      if (Math.random() > 0.5) {
        processingOrders.value += Math.floor(Math.random() * 2) - 1
        completedOrders.value += Math.floor(Math.random() * 2)
      }
    }
  }, 5000)

  // 清理定时器
  return () => clearInterval(interval)
})
</script>

<style scoped>
.basic-info-card {
  min-height: 200px;
}
</style>
