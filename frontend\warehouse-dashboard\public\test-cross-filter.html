<!DOCTYPE html>
<html>
<head>
    <title>交叉筛选功能自动化测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        #test-results { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🧪 交叉筛选功能自动化测试</h1>
    
    <div class="test-section">
        <h2>测试控制</h2>
        <button class="test-button" onclick="runAllTests()">🚀 运行所有测试</button>
        <button class="test-button" onclick="clearResults()">🗑️ 清除结果</button>
        <button class="test-button" onclick="openMainApp()">🔗 打开主应用</button>
    </div>

    <div class="test-section">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        let testResults = [];
        
        function addResult(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({ type, message, timestamp });
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.type}">
                    <strong>[${result.timestamp}]</strong> ${result.message}
                </div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }
        
        function clearResults() {
            testResults = [];
            updateResultsDisplay();
        }
        
        function openMainApp() {
            window.open('http://localhost:5175/', '_blank');
        }
        
        async function runAllTests() {
            addResult('info', '🧪 开始运行交叉筛选功能测试套件...');
            
            try {
                // 测试1: 检查主应用可访问性
                await testMainAppAccessibility();
                
                // 测试2: 检查数据源API
                await testDataSourceAPI();
                
                // 测试3: 检查筛选Store
                await testFilterStore();
                
                // 测试4: 检查地图数据
                await testMapData();
                
                // 测试5: 模拟筛选流程
                await testFilterFlow();
                
                addResult('success', '✅ 所有测试完成！');
                
            } catch (error) {
                addResult('error', `❌ 测试过程中发生错误: ${error.message}`);
            }
        }
        
        async function testMainAppAccessibility() {
            addResult('info', '🔍 测试1: 检查主应用可访问性...');
            
            try {
                const response = await fetch('http://localhost:5175/');
                if (response.ok) {
                    addResult('success', '✅ 主应用可正常访问');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('error', `❌ 主应用访问失败: ${error.message}`);
                throw error;
            }
        }
        
        async function testDataSourceAPI() {
            addResult('info', '🔍 测试2: 检查数据源API...');
            
            // 模拟数据源测试
            const testData = {
                'monthlySales': {
                    '全国': [
                        { x: '1月', y: 2100 },
                        { x: '2月', y: 1800 },
                        { x: '3月', y: 2500 },
                        { x: '4月', y: 3200 },
                        { x: '5月', y: 3000 },
                        { x: '6月', y: 2800 }
                    ],
                    '广东': [
                        { x: '1月', y: 350 },
                        { x: '2月', y: 300 },
                        { x: '3月', y: 400 },
                        { x: '4月', y: 550 },
                        { x: '5月', y: 500 },
                        { x: '6月', y: 450 }
                    ],
                    '浙江': [
                        { x: '1月', y: 250 },
                        { x: '2月', y: 220 },
                        { x: '3月', y: 310 },
                        { x: '4月', y: 400 },
                        { x: '5月', y: 380 },
                        { x: '6月', y: 350 }
                    ]
                }
            };
            
            // 验证数据结构
            if (testData.monthlySales['全国'].length === 6) {
                addResult('success', '✅ 全国数据结构正确 (6个月数据)');
            } else {
                addResult('error', '❌ 全国数据结构错误');
            }
            
            if (testData.monthlySales['广东'].length === 6) {
                addResult('success', '✅ 广东数据结构正确 (6个月数据)');
            } else {
                addResult('error', '❌ 广东数据结构错误');
            }
            
            if (testData.monthlySales['浙江'].length === 6) {
                addResult('success', '✅ 浙江数据结构正确 (6个月数据)');
            } else {
                addResult('error', '❌ 浙江数据结构错误');
            }
        }
        
        async function testFilterStore() {
            addResult('info', '🔍 测试3: 检查筛选Store逻辑...');
            
            // 模拟筛选状态管理
            let currentFilter = { province: null };
            
            // 测试设置筛选
            function setFilter(province) {
                currentFilter.province = province;
                return currentFilter;
            }
            
            // 测试清除筛选
            function clearFilter() {
                currentFilter.province = null;
                return currentFilter;
            }
            
            // 执行测试
            const result1 = setFilter('广东');
            if (result1.province === '广东') {
                addResult('success', '✅ 筛选设置功能正常');
            } else {
                addResult('error', '❌ 筛选设置功能异常');
            }
            
            const result2 = clearFilter();
            if (result2.province === null) {
                addResult('success', '✅ 筛选清除功能正常');
            } else {
                addResult('error', '❌ 筛选清除功能异常');
            }
        }
        
        async function testMapData() {
            addResult('info', '🔍 测试4: 检查地图数据...');
            
            try {
                const response = await fetch('/maps/china.json');
                if (response.ok) {
                    const mapData = await response.json();
                    if (mapData.type === 'FeatureCollection' && mapData.features) {
                        addResult('success', `✅ 地图数据格式正确 (包含 ${mapData.features.length} 个省份)`);
                        
                        // 检查是否包含关键省份
                        const provinces = mapData.features.map(f => f.properties.name);
                        const keyProvinces = ['北京市', '广东省', '浙江省', '上海市'];
                        const foundProvinces = keyProvinces.filter(p => 
                            provinces.some(name => name.includes(p.replace('省', '').replace('市', '')))
                        );
                        
                        addResult('success', `✅ 找到关键省份: ${foundProvinces.length}/${keyProvinces.length}`);
                    } else {
                        addResult('error', '❌ 地图数据格式错误');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('error', `❌ 地图数据加载失败: ${error.message}`);
            }
        }
        
        async function testFilterFlow() {
            addResult('info', '🔍 测试5: 模拟筛选流程...');
            
            // 模拟完整的筛选流程
            const steps = [
                { action: '初始状态', filter: null, expected: '全国数据' },
                { action: '点击广东', filter: '广东', expected: '广东数据' },
                { action: '点击浙江', filter: '浙江', expected: '浙江数据' },
                { action: '重复点击浙江', filter: null, expected: '全国数据' },
                { action: '点击空白区域', filter: null, expected: '全国数据' }
            ];
            
            for (const step of steps) {
                await new Promise(resolve => setTimeout(resolve, 100)); // 模拟延迟
                
                // 模拟数据获取
                const mockData = step.filter ? 
                    `${step.filter}省份的月度数据` : 
                    '全国月度总览数据';
                
                if (mockData.includes(step.expected.replace('数据', ''))) {
                    addResult('success', `✅ ${step.action}: 返回${step.expected}`);
                } else {
                    addResult('error', `❌ ${step.action}: 数据不匹配`);
                }
            }
            
            addResult('success', '✅ 筛选流程测试完成');
        }
        
        // 页面加载时显示欢迎信息
        window.onload = function() {
            addResult('info', '🎯 交叉筛选功能测试工具已就绪');
            addResult('info', '📋 点击"运行所有测试"开始自动化测试');
        };
    </script>
</body>
</html>
