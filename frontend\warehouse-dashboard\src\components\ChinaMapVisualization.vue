<template>
  <div class="china-map-container" ref="mapContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

// Props
interface Props {
  data?: Array<{
    name: string;
    value: number;
    coords?: [number, number];
  }>;
  height?: string;
  theme?: 'dark' | 'light';
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  height: '100%',
  theme: 'dark'
});

// Refs
const mapContainer = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 模拟数据 - 主要城市物流数据
const defaultData = [
  { name: '北京', value: 2340, coords: [116.46, 39.92] },
  { name: '上海', value: 1890, coords: [121.48, 31.22] },
  { name: '广州', value: 1680, coords: [113.23, 23.16] },
  { name: '深圳', value: 1560, coords: [114.07, 22.62] },
  { name: '杭州', value: 1200, coords: [120.19, 30.26] },
  { name: '武汉', value: 980, coords: [114.31, 30.52] },
  { name: '成都', value: 890, coords: [104.06, 30.67] },
  { name: '西安', value: 780, coords: [108.95, 34.27] },
  { name: '南京', value: 720, coords: [118.78, 32.04] },
  { name: '重庆', value: 650, coords: [106.54, 29.59] },
  { name: '天津', value: 580, coords: [117.20, 39.13] },
  { name: '苏州', value: 520, coords: [120.62, 31.32] },
  { name: '青岛', value: 480, coords: [120.33, 36.07] },
  { name: '长沙', value: 450, coords: [112.94, 28.23] },
  { name: '大连', value: 420, coords: [121.62, 38.92] }
];

// 物流路线数据
const routeData = [
  { fromName: '北京', toName: '上海', coords: [[116.46, 39.92], [121.48, 31.22]] },
  { fromName: '北京', toName: '广州', coords: [[116.46, 39.92], [113.23, 23.16]] },
  { fromName: '上海', toName: '深圳', coords: [[121.48, 31.22], [114.07, 22.62]] },
  { fromName: '广州', toName: '成都', coords: [[113.23, 23.16], [104.06, 30.67]] },
  { fromName: '北京', toName: '武汉', coords: [[116.46, 39.92], [114.31, 30.52]] },
  { fromName: '上海', toName: '杭州', coords: [[121.48, 31.22], [120.19, 30.26]] },
  { fromName: '武汉', toName: '重庆', coords: [[114.31, 30.52], [106.54, 29.59]] },
  { fromName: '成都', toName: '西安', coords: [[104.06, 30.67], [108.95, 34.27]] }
];

// 图表配置
const getMapOption = () => {
  const mapData = props.data.length > 0 ? props.data : defaultData;

  return {
    backgroundColor: 'transparent',
    title: {
      text: '全国物流分布热力图',
      left: 'center',
      top: '3%',
      textStyle: {
        color: '#7BDEFF',
        fontSize: 16,
        fontWeight: 'bold',
        textShadowColor: 'rgba(123, 222, 255, 0.5)',
        textShadowBlur: 10
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(6, 22, 74, 0.95)',
      borderColor: '#00D4FF',
      borderWidth: 2,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      extraCssText: 'box-shadow: 0 0 20px rgba(0, 212, 255, 0.5); border-radius: 8px;',
      formatter: function(params: any) {
        if (params.seriesType === 'scatter') {
          return `<div style="padding: 5px;">
            <div style="color: #7BDEFF; font-weight: bold; margin-bottom: 5px;">${params.data.name}</div>
            <div style="color: #00FF88;">订单量: ${params.data.value}</div>
            <div style="color: #A0D8EF; font-size: 10px; margin-top: 3px;">点击查看详情</div>
          </div>`;
        } else if (params.seriesType === 'lines') {
          return `<div style="padding: 5px;">
            <div style="color: #7BDEFF; font-weight: bold;">物流路线</div>
            <div style="color: #00FF88;">${params.data.fromName} → ${params.data.toName}</div>
          </div>`;
        }
        return params.name;
      }
    },
    // 使用简化的背景地图
    grid: {
      left: '5%',
      right: '5%',
      top: '15%',
      bottom: '5%',
      containLabel: true
    },
    series: [
      // 散点图 - 城市数据（使用直角坐标系）
      {
        name: '物流节点',
        type: 'scatter',
        data: mapData.map((item, index) => ({
          name: item.name,
          value: [index * 100 + Math.random() * 50, item.value + Math.random() * 100, item.value],
          symbolSize: Math.sqrt(item.value / 10) + 15
        })),
        symbol: 'circle',
        symbolSize: function(val: number[]) {
          return Math.sqrt(val[2] / 10) + 15;
        },
        itemStyle: {
          color: function(params: any) {
            const value = params.data.value[2];
            if (value > 1500) return {
              type: 'radial',
              x: 0.5, y: 0.5, r: 0.5,
              colorStops: [
                { offset: 0, color: '#FF6B6B' },
                { offset: 1, color: '#FF4757' }
              ]
            };
            if (value > 1000) return {
              type: 'radial',
              x: 0.5, y: 0.5, r: 0.5,
              colorStops: [
                { offset: 0, color: '#4ECDC4' },
                { offset: 1, color: '#26A69A' }
              ]
            };
            if (value > 500) return {
              type: 'radial',
              x: 0.5, y: 0.5, r: 0.5,
              colorStops: [
                { offset: 0, color: '#00D4FF' },
                { offset: 1, color: '#0096FF' }
              ]
            };
            return {
              type: 'radial',
              x: 0.5, y: 0.5, r: 0.5,
              colorStops: [
                { offset: 0, color: '#96CEB4' },
                { offset: 1, color: '#4CAF50' }
              ]
            };
          },
          shadowBlur: 15,
          shadowColor: function(params: any) {
            const value = params.data.value[2];
            if (value > 1500) return '#FF6B6B';
            if (value > 1000) return '#4ECDC4';
            if (value > 500) return '#00D4FF';
            return '#96CEB4';
          },
          opacity: 0.8
        },
        emphasis: {
          scale: true,
          scaleSize: 10,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 20,
            opacity: 1
          }
        },
        label: {
          show: true,
          position: 'top',
          color: '#7BDEFF',
          fontSize: 10,
          fontWeight: 'bold',
          formatter: function(params: any) {
            return `${params.data.name}\n${params.data.value[2]}`;
          },
          textStyle: {
            textShadowColor: 'rgba(0, 0, 0, 0.8)',
            textShadowBlur: 3
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx: number) {
          return idx * 100;
        }
      }
    ],
    xAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 1000
    },
    yAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 3000
    },
    visualMap: {
      min: 0,
      max: 2500,
      left: '3%',
      bottom: '5%',
      text: ['高', '低'],
      textStyle: {
        color: '#7BDEFF',
        fontSize: 10
      },
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      },
      orient: 'horizontal',
      itemWidth: 15,
      itemHeight: 8,
      backgroundColor: 'rgba(6, 22, 74, 0.8)',
      borderColor: 'rgba(0, 212, 255, 0.5)',
      borderWidth: 1,
      borderRadius: 5,
      padding: 8
    }
  };
};

// 初始化图表
const initChart = async () => {
  if (!mapContainer.value) return;

  try {
    chartInstance = echarts.init(mapContainer.value, props.theme);
    chartInstance.setOption(getMapOption());

    // 添加点击事件
    chartInstance.on('click', (params: any) => {
      if (params.componentType === 'series' && params.seriesType === 'scatter') {
        console.log('点击城市:', params.data.name);
        // 这里可以添加城市详情展示逻辑
      }
    });

    // 响应式处理
    const resizeHandler = () => {
      chartInstance?.resize();
    };
    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
};

// 更新图表数据
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getMapOption());
  }
};

// 生命周期
onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 监听数据变化
watch(() => props.data, updateChart, { deep: true });
</script>

<style scoped>
.china-map-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
  background: radial-gradient(circle at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
  border-radius: 8px;
}
</style>
