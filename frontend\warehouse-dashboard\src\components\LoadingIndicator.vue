<template>
  <div class="loading-container">
    <div class="loading-content">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <div class="loading-text">
        <h2>数据加载中...</h2>
        <p>正在获取最新的仪表盘数据，请稍候</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件不需要任何props或逻辑，纯展示组件
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
  padding: 2rem;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  text-align: center;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid #7BDEFF;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.3s;
  border-top-color: #1E90FF;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.15s;
  border-top-color: #00CED1;
}

.spinner-ring:nth-child(4) {
  border-top-color: #00FF7F;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text h2 {
  color: #7BDEFF;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loading-text p {
  color: #B0E0E6;
  font-size: 1rem;
  margin: 0;
  opacity: 0.8;
}

/* 添加一些动画效果 */
.loading-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container {
    padding: 1rem;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
  
  .loading-text h2 {
    font-size: 1.5rem;
  }
  
  .loading-text p {
    font-size: 0.9rem;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .loading-container {
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  }
}
</style>
