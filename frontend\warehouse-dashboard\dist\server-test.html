<!DOCTYPE html>
<html>
<head>
    <title>服务器连接测试</title>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%); 
            color: white; 
            line-height: 1.6;
            min-height: 100vh;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(16, 33, 62, 0.9);
            border-radius: 12px;
            border: 2px solid #40e0d0;
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
        }
        .header h1 {
            color: #7BDEFF;
            font-size: 2.2rem;
            margin: 0;
            text-shadow: 0 0 25px rgba(123, 222, 255, 0.7);
        }
        .status-item {
            margin: 15px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border-left: 4px solid #00FF88;
        }
        .link-button {
            display: inline-block;
            padding: 12px 20px;
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.2), rgba(64, 224, 208, 0.2));
            border: 1px solid rgba(0, 150, 255, 0.4);
            border-radius: 8px;
            color: #7BDEFF;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .link-button:hover {
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.3), rgba(64, 224, 208, 0.3));
            border-color: rgba(0, 150, 255, 0.6);
            color: #FFFFFF;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 服务器连接测试</h1>
        <div style="margin-top: 15px; color: #B3E5FC; font-size: 0.9rem;">
            测试时间: <span id="test-time"></span>
        </div>
    </div>

    <div class="status-item">
        <h3>✅ 服务器状态检查</h3>
        <p><strong>服务器地址</strong>: http://localhost:5173/</p>
        <p><strong>当前页面</strong>: 如果您能看到这个页面，说明服务器运行正常</p>
        <p><strong>Vite版本</strong>: 7.0.4</p>
        <p><strong>Vue版本</strong>: 3.x</p>
    </div>

    <div class="status-item">
        <h3>🔗 快速访问链接</h3>
        <a href="http://localhost:5173/" class="link-button" target="_blank">🏠 主应用</a>
        <a href="http://localhost:5173/final-test-report.html" class="link-button" target="_blank">📊 最终测试报告</a>
        <a href="http://localhost:5173/final-system-check.html" class="link-button" target="_blank">🚀 系统检查</a>
    </div>

    <div class="status-item">
        <h3>🛠️ 故障排除步骤</h3>
        <ol>
            <li><strong>确认地址</strong>: 使用 http://localhost:5173/ (不是5175)</li>
            <li><strong>硬刷新</strong>: 按 Ctrl+F5 清除缓存</li>
            <li><strong>检查控制台</strong>: 按 F12 查看是否有错误信息</li>
            <li><strong>尝试无痕模式</strong>: 排除扩展程序干扰</li>
            <li><strong>检查防火墙</strong>: 确保5173端口未被阻止</li>
        </ol>
    </div>

    <div class="status-item">
        <h3>📱 浏览器兼容性</h3>
        <p><strong>推荐浏览器</strong>:</p>
        <ul>
            <li>Chrome 90+ ✅</li>
            <li>Firefox 88+ ✅</li>
            <li>Edge 90+ ✅</li>
            <li>Safari 14+ ✅</li>
        </ul>
        <p><strong>不支持</strong>: Internet Explorer</p>
    </div>

    <script>
        // 设置测试时间
        document.getElementById('test-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 自动检测主应用状态
        function checkMainApp() {
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ 主应用服务器连接正常');
                        document.body.style.borderTop = '5px solid #00FF88';
                    } else {
                        console.log('⚠️ 主应用响应异常');
                        document.body.style.borderTop = '5px solid #FFD700';
                    }
                })
                .catch(error => {
                    console.error('❌ 主应用连接失败:', error);
                    document.body.style.borderTop = '5px solid #FF6B6B';
                });
        }
        
        // 页面加载完成后执行检查
        window.onload = function() {
            console.log('🔍 服务器连接测试页面加载完成');
            console.log('📍 当前服务器: http://localhost:5173/');
            console.log('🎯 如果您能看到这个页面，说明服务器运行正常');
            
            setTimeout(checkMainApp, 1000);
        };
    </script>
</body>
</html>
