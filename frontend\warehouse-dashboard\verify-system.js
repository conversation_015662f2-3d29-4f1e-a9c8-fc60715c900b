#!/usr/bin/env node

/**
 * 动态仪表盘系统验证脚本
 * 检查系统的各个组件是否正常工作
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 动态仪表盘系统验证开始...\n');

// 验证结果
const results = {
  passed: 0,
  failed: 0,
  warnings: 0
};

// 检查函数
function check(description, condition, isWarning = false) {
  const status = condition ? '✅' : (isWarning ? '⚠️' : '❌');
  const type = condition ? 'PASS' : (isWarning ? 'WARN' : 'FAIL');
  
  console.log(`${status} ${description}`);
  
  if (condition) {
    results.passed++;
  } else if (isWarning) {
    results.warnings++;
  } else {
    results.failed++;
  }
}

// 1. 检查文件结构
console.log('📁 检查文件结构...');
check('layout.ts 存在', fs.existsSync('src/stores/layout.ts'));
check('DashboardView.vue 存在', fs.existsSync('src/views/DashboardView.vue'));
check('BasicInfoCard.vue 存在', fs.existsSync('src/components/cards/BasicInfoCard.vue'));
check('SalesChartCard.vue 存在', fs.existsSync('src/components/cards/SalesChartCard.vue'));
check('KeyMetricCard.vue 存在', fs.existsSync('src/components/cards/KeyMetricCard.vue'));
check('DynamicDashboardTest.vue 存在', fs.existsSync('src/DynamicDashboardTest.vue'));

// 2. 检查依赖
console.log('\n📦 检查依赖包...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

check('gridstack 已安装', !!dependencies.gridstack);
check('vue 已安装', !!dependencies.vue);
check('pinia 已安装', !!dependencies.pinia);
check('echarts 已安装', !!dependencies.echarts);
check('typescript 已安装', !!dependencies.typescript);

// 3. 检查配置文件
console.log('\n⚙️ 检查配置文件...');
check('vite.config.js 存在', fs.existsSync('vite.config.js'));
check('tsconfig.json 存在', fs.existsSync('tsconfig.json'));
check('tailwind.config.js 存在', fs.existsSync('tailwind.config.js'));

// 4. 检查代码质量
console.log('\n🔍 检查代码质量...');

// 检查 layout.ts
if (fs.existsSync('src/stores/layout.ts')) {
  const layoutContent = fs.readFileSync('src/stores/layout.ts', 'utf8');
  check('layout.ts 包含 LayoutItem 接口', layoutContent.includes('interface LayoutItem'));
  check('layout.ts 包含 useLayoutStore', layoutContent.includes('useLayoutStore'));
  check('layout.ts 包含 localStorage 检查', layoutContent.includes('typeof window'));
}

// 检查 DashboardView.vue
if (fs.existsSync('src/views/DashboardView.vue')) {
  const dashboardContent = fs.readFileSync('src/views/DashboardView.vue', 'utf8');
  check('DashboardView 导入 GridStack', dashboardContent.includes('GridStack'));
  check('DashboardView 包含 gridStackRef', dashboardContent.includes('gridStackRef'));
  check('DashboardView 包含 initGridStack', dashboardContent.includes('initGridStack'));
}

// 检查 App.vue
if (fs.existsSync('src/App.vue')) {
  const appContent = fs.readFileSync('src/App.vue', 'utf8');
  check('App.vue 导入 DashboardView', appContent.includes('DashboardView'));
  check('App.vue 包含动态仪表盘按钮', appContent.includes('动态仪表盘'));
  check('App.vue 包含测试按钮', appContent.includes('仪表盘测试'));
}

// 5. 检查组件完整性
console.log('\n🧩 检查组件完整性...');

const cardComponents = [
  'src/components/cards/BasicInfoCard.vue',
  'src/components/cards/SalesChartCard.vue',
  'src/components/cards/KeyMetricCard.vue'
];

cardComponents.forEach(componentPath => {
  if (fs.existsSync(componentPath)) {
    const content = fs.readFileSync(componentPath, 'utf8');
    const componentName = path.basename(componentPath, '.vue');
    
    check(`${componentName} 包含 template`, content.includes('<template>'));
    check(`${componentName} 包含 script setup`, content.includes('<script setup'));
    check(`${componentName} 包含 TypeScript`, content.includes('lang="ts"'));
    check(`${componentName} 包含 props 定义`, content.includes('Props') || content.includes('defineProps'));
  }
});

// 6. 检查样式和资源
console.log('\n🎨 检查样式和资源...');
check('main.css 存在', fs.existsSync('src/assets/main.css'));
check('GridStack CSS 导入', 
  fs.existsSync('src/views/DashboardView.vue') && 
  fs.readFileSync('src/views/DashboardView.vue', 'utf8').includes('gridstack.min.css')
);

// 7. 检查 TypeScript 配置
console.log('\n📝 检查 TypeScript 配置...');
if (fs.existsSync('tsconfig.json')) {
  try {
    const tsConfigContent = fs.readFileSync('tsconfig.json', 'utf8');
    // 简单的注释移除（仅用于验证）
    const cleanContent = tsConfigContent.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '');
    const tsConfig = JSON.parse(cleanContent);
    check('TypeScript 启用严格模式', tsConfig.compilerOptions?.strict === true, true);
    check('TypeScript 包含路径别名', !!tsConfig.compilerOptions?.paths, true);
  } catch (e) {
    check('TypeScript 配置文件格式正确', false, true);
  }
}

// 8. 检查 Vite 配置
console.log('\n⚡ 检查 Vite 配置...');
if (fs.existsSync('vite.config.js')) {
  const viteContent = fs.readFileSync('vite.config.js', 'utf8');
  check('Vite 配置包含 Vue 插件', viteContent.includes('@vitejs/plugin-vue'));
  check('Vite 配置包含路径别名', viteContent.includes('resolve') && viteContent.includes('alias'));
}

// 输出结果
console.log('\n📊 验证结果汇总:');
console.log(`✅ 通过: ${results.passed}`);
console.log(`❌ 失败: ${results.failed}`);
console.log(`⚠️ 警告: ${results.warnings}`);

const total = results.passed + results.failed + results.warnings;
const successRate = ((results.passed / total) * 100).toFixed(1);

console.log(`\n🎯 成功率: ${successRate}%`);

if (results.failed === 0) {
  console.log('\n🎉 系统验证通过！所有核心功能都已正确配置。');
  console.log('\n🚀 下一步操作:');
  console.log('1. 运行 npm run dev 启动开发服务器');
  console.log('2. 访问 http://localhost:5173/');
  console.log('3. 点击"仪表盘测试"按钮进行功能测试');
  console.log('4. 点击"动态仪表盘"按钮体验拖拽功能');
} else {
  console.log('\n⚠️ 发现问题，请检查失败的项目并修复后重新验证。');
  process.exit(1);
}

console.log('\n📚 更多信息请查看 SYSTEM_STATUS.md 文件');
