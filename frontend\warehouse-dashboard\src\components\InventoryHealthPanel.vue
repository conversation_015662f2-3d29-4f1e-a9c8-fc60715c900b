<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <div class="health-metrics">
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">库存周转率</span>
            <span class="metric-value">{{ turnoverRate }}</span>
            <span class="metric-unit">次/日</span>
          </div>
          <div class="progress-container">
            <div class="progress-bar">
              <div 
                class="progress-fill turnover" 
                :style="{ width: turnoverProgress + '%' }"
              ></div>
            </div>
            <div class="progress-text">{{ turnoverProgress }}%</div>
          </div>
        </div>
        
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">库存准确率</span>
            <span class="metric-value">{{ accuracyRate }}</span>
            <span class="metric-unit">%</span>
          </div>
          <div class="progress-container">
            <div class="progress-bar">
              <div 
                class="progress-fill accuracy" 
                :style="{ width: accuracyRate + '%' }"
              ></div>
            </div>
            <div class="progress-text">{{ accuracyRate }}%</div>
          </div>
        </div>
        
        <div class="health-summary">
          <div class="summary-item">
            <div class="summary-label">健康度评级</div>
            <div class="summary-value" :class="healthGrade.class">
              {{ healthGrade.text }}
            </div>
          </div>
          <div class="summary-item">
            <div class="summary-label">改善建议</div>
            <div class="summary-suggestion">{{ suggestion }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '库存健康度'
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// 计算库存指标
const turnoverRate = computed(() => {
  return props.data.turnover_rate || 2.3
})

const accuracyRate = computed(() => {
  return props.data.accuracy_rate || 96.8
})

// 周转率进度（基于目标值计算）
const turnoverProgress = computed(() => {
  const target = 3.0 // 目标周转率
  return Math.min((turnoverRate.value / target) * 100, 100)
})

// 健康度评级
const healthGrade = computed(() => {
  const turnover = turnoverRate.value
  const accuracy = accuracyRate.value
  
  if (turnover >= 2.5 && accuracy >= 95) {
    return { text: '优秀', class: 'excellent' }
  } else if (turnover >= 2.0 && accuracy >= 90) {
    return { text: '良好', class: 'good' }
  } else if (turnover >= 1.5 && accuracy >= 85) {
    return { text: '一般', class: 'average' }
  } else {
    return { text: '需改善', class: 'poor' }
  }
})

// 改善建议
const suggestion = computed(() => {
  const turnover = turnoverRate.value
  const accuracy = accuracyRate.value
  
  if (turnover < 2.0 && accuracy < 90) {
    return '优化库存结构，提升盘点频次'
  } else if (turnover < 2.0) {
    return '加快库存周转，减少滞销商品'
  } else if (accuracy < 90) {
    return '加强库存管理，提高盘点准确性'
  } else {
    return '保持当前良好状态'
  }
})
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 20px;
}

.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.metric-item {
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.metric-header {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 10px;
}

.metric-label {
  font-size: 1rem;
  color: #7BDEFF;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
}

.metric-unit {
  font-size: 0.8rem;
  color: #7BDEFF;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.turnover {
  background: linear-gradient(90deg, #32CD32, #00FF7F);
}

.progress-fill.accuracy {
  background: linear-gradient(90deg, #1E90FF, #00CED1);
}

.progress-text {
  font-size: 0.9rem;
  color: #ffffff;
  min-width: 40px;
}

.health-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: auto;
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 0.9rem;
  color: #7BDEFF;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 4px;
}

.summary-value.excellent {
  color: #00FF7F;
  background: rgba(0, 255, 127, 0.1);
}

.summary-value.good {
  color: #32CD32;
  background: rgba(50, 205, 50, 0.1);
}

.summary-value.average {
  color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
}

.summary-value.poor {
  color: #FF6347;
  background: rgba(255, 99, 71, 0.1);
}

.summary-suggestion {
  font-size: 0.8rem;
  color: #ffffff;
  background: rgba(30, 144, 255, 0.1);
  padding: 8px;
  border-radius: 4px;
  border: 1px solid rgba(30, 144, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-summary {
    grid-template-columns: 1fr;
  }
  
  .metric-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
