<template>
  <div class="sales-chart-card h-full bg-gradient-to-br from-green-600 to-green-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">📈</div>
    </div>

    <div class="h-full">
      <div ref="chartContainer" class="w-full" style="height: calc(100% - 60px);"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { useDataSourceStore, type DataItem } from '@/stores/dataSource'
import { sciFiTheme } from '@/echarts-theme'
import { useFilterStore } from '@/stores/filterStore.ts'

interface Props {
  title?: string
  dataSourceId?: string | null
  widgetId?: string // 添加 widgetId 属性
  chartType?: string // 新增 chartType 属性
}

const props = withDefaults(defineProps<Props>(), {
  title: '商品销售排行',
  dataSourceId: null,
  widgetId: undefined,
  chartType: 'line' // 默认为折线图
})

const chartContainer = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
const dataSourceStore = useDataSourceStore()
const filterStore = useFilterStore() // 2. 获取 filter store 实例

// 默认销售数据（当没有选择数据源时使用）
const defaultSalesData: DataItem[] = [
  { name: '电子产品', value: 2580 },
  { name: '服装鞋帽', value: 1890 },
  { name: '家居用品', value: 1456 },
  { name: '食品饮料', value: 1234 },
  { name: '图书文具', value: 987 },
  { name: '运动户外', value: 756 },
]

// 核心逻辑：获取并更新图表
const updateChartData = async () => {
  if (!chartInstance) return

  let data: DataItem[] = []

  if (props.dataSourceId) {
    try {
      // 显示加载状态
      chartInstance.showLoading('default', {
        text: '加载数据中...',
        color: '#4ECDC4',
        textColor: '#fff',
        maskColor: 'rgba(0, 0, 0, 0.8)'
      })

      // 3. 在获取数据时，传入当前的筛选条件
      data = await dataSourceStore.fetchData(props.dataSourceId, filterStore.filters)
      chartInstance.hideLoading()

      if (data.length === 0) {
        // 如果没有数据，显示空状态
        chartInstance.clear()
        chartInstance.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 16
            }
          }
        })
        return
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
      chartInstance.hideLoading()
      // 发生错误时使用默认数据
      data = defaultSalesData
    }
  } else {
    // 没有选择数据源时使用默认数据
    data = defaultSalesData
  }

  // 更新图表配置 - 支持时间序列数据和饼图数据
  let option: any = {}

  // 检查数据格式，决定使用哪种图表类型
  if (data.length > 0 && data[0].x && data[0].y) {
    // 时间序列数据，使用折线图
    option = {
      title: {
        text: props.title,
        left: 'center',
        top: '8px',
        textStyle: {
          color: '#EFEFEF',
          fontSize: 14,
          fontWeight: 'bold',
          fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(10, 22, 52, 0.9)',
        borderColor: 'rgba(0, 150, 255, 0.6)',
        borderWidth: 1,
        textStyle: {
          color: '#EFEFEF',
          fontSize: 11,
          fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
        },
        extraCssText: 'backdrop-filter: blur(5px); box-shadow: 0 0 10px rgba(0, 150, 255, 0.3);'
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '20%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: data.map((item: any) => item.x),
        axisLine: {
          lineStyle: { color: '#4ECDC4' }
        },
        axisLabel: {
          color: '#EFEFEF',
          fontSize: 10,
          fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: { color: '#4ECDC4' }
        },
        axisLabel: {
          color: '#EFEFEF',
          fontSize: 10,
          fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
        },
        splitLine: {
          lineStyle: { color: 'rgba(78, 205, 196, 0.2)' }
        }
      },
      series: [
        {
          type: 'line',
          data: data.map((item: any) => item.y),
          smooth: true,
          lineStyle: {
            color: '#00D4FF',
            width: 2
          },
          itemStyle: {
            color: '#00D4FF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 212, 255, 0.1)' }
              ]
            }
          }
        }
      ]
    }
  } else {
    // 普通数据，使用饼图
    option = {
      title: {
        text: props.title,
        left: 'center',
        top: '8px',
        textStyle: {
          color: '#EFEFEF',
          fontSize: 14,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(10, 22, 52, 0.9)',
        borderColor: 'rgba(0, 150, 255, 0.6)',
        borderWidth: 1,
        textStyle: {
          color: '#EFEFEF',
          fontSize: 11
        },
        formatter: '{b}: {c} ({d}%)',
        extraCssText: 'backdrop-filter: blur(5px); box-shadow: 0 0 10px rgba(0, 150, 255, 0.3);'
      },
      legend: {
        show: false // 在小组件中隐藏图例以节省空间
      },
      series: [
        {
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '58%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 150, 255, 0.6)'
          }
        },
        label: {
          show: true,
          position: 'outside',
          color: '#EFEFEF',
          fontSize: 9,
          formatter: '{b}: {c}',
          textStyle: {
            textShadowColor: 'rgba(0, 0, 0, 0.8)',
            textShadowBlur: 3
          }
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 150, 255, 0.6)',
            width: 1
          }
        },
        itemStyle: {
          borderRadius: 8,
          borderColor: 'rgba(0, 150, 255, 0.4)',
          borderWidth: 2,
          shadowColor: 'rgba(0, 150, 255, 0.3)',
          shadowBlur: 8
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: () => Math.random() * 200
        }
      ]
      // 颜色由主题控制，不需要单独设置
    }
  }

  chartInstance.setOption(option, true) // true表示不合并，完全替换
}

const initChart = () => {
  if (!chartContainer.value) return

  // 注册并使用科技主题
  echarts.registerTheme('sci-fi', sciFiTheme)
  chartInstance = echarts.init(chartContainer.value, 'sci-fi')

  // 初始加载数据
  updateChartData()

  // 响应式调整
  window.addEventListener('resize', handleResize)
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 4. 监听整个 filters 对象的变化
watch(() => filterStore.filters, () => {
  updateChartData()
}, { deep: true })

// 同时监听 dataSourceId 和 title 的变化
watch(() => [props.dataSourceId, props.title], () => {
  updateChartData()
}, { deep: true })

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', handleResize)
  }
})
</script>

<style scoped>
.sales-chart-card {
  min-height: 200px;
}
</style>
