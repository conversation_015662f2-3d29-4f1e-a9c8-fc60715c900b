<template>
  <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200 transition-all duration-300 hover:shadow-lg">
    <!-- 图表标题 -->
    <div class="chart-header mb-4">
      <h3 class="text-lg font-semibold text-slate-800 mb-2">{{ title }}</h3>
      <div class="text-sm text-slate-600" v-if="subtitle">{{ subtitle }}</div>
    </div>

    <!-- ECharts 容器 -->
    <div
      ref="chartContainer"
      class="chart-container"
      :style="{ width: '100%', height: chartHeight }"
    ></div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>

    <!-- 无数据状态 -->
    <div v-if="!loading && (!chartData || chartData.length === 0)" class="no-data">
      <div class="no-data-icon">📊</div>
      <div class="no-data-text">暂无数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useDashboardStore } from '../stores/dashboard'
import * as echarts from 'echarts'
import type { PieChartProps, RegionName } from '../types/index'

// Props 定义
const props = withDefaults(defineProps<PieChartProps>(), {
  chartData: () => [],
  title: '区域分布',
  subtitle: '点击区域查看详细数据',
  loading: false,
  showPercentage: true,
  enableAnimation: true,
  chartHeight: '100%',
  themeColor: '#1E90FF'
})



// 响应式数据
const chartContainer = ref<HTMLDivElement | null>(null)
const chartInstance = ref<echarts.EChartsType | null>(null)

// 实例化Dashboard Store
const dashboardStore = useDashboardStore()

// 科技感配色方案
const colorPalette = [
  '#1E90FF', // 科技蓝
  '#00CED1', // 深青色
  '#FFD700', // 金色
  '#00FF7F', // 春绿色
  '#FF6B6B', // 珊瑚红
  '#9370DB', // 中紫色
  '#20B2AA', // 浅海绿
  '#FFA500', // 橙色
  '#87CEEB', // 天蓝色
  '#DDA0DD'  // 梅红色
]

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return

  // 销毁已存在的实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  // 创建新的图表实例
  chartInstance.value = echarts.init(chartContainer.value)

  // 绑定点击事件
  chartInstance.value.on('click', (params: any) => {
    if (params.name) {
      console.log('🎯 饼图点击事件:', params.name)
      // 调用Store的action来选择区域
      dashboardStore.selectRegion(params.name as RegionName)
    }
  })

  // 设置图表配置
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance.value || !props.chartData || props.chartData.length === 0) {
    return
  }

  // 计算总值用于百分比计算
  const total = props.chartData.reduce((sum, item) => sum + item.value, 0)

  // 处理数据，添加百分比
  const processedData = props.chartData.map((item, index) => ({
    ...item,
    percentage: ((item.value / total) * 100).toFixed(1),
    itemStyle: {
      color: colorPalette[index % colorPalette.length]
    }
  }))

  const option = {
    // 背景透明
    backgroundColor: 'transparent',

    // 图例配置
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: {
        color: '#7BDEFF',
        fontSize: 12,
        fontFamily: 'Microsoft YaHei'
      },
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 8,
      formatter: (name: string) => {
        const item = processedData.find(d => d.name === name)
        if (item && props.showPercentage) {
          return `${name} (${item.percentage}%)`
        }
        return name
      }
    },

    // 提示框配置
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(14, 38, 92, 0.9)',
      borderColor: '#1E90FF',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: (params: any) => {
        const percentage = ((params.value / total) * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="color: #7BDEFF; font-weight: bold; margin-bottom: 4px;">
              ${params.name}
            </div>
            <div style="display: flex; justify-content: space-between; gap: 20px;">
              <span>数值:</span>
              <span style="color: #00FF7F; font-weight: bold;">
                ${params.value.toLocaleString()}
              </span>
            </div>
            <div style="display: flex; justify-content: space-between; gap: 20px;">
              <span>占比:</span>
              <span style="color: #FFD700; font-weight: bold;">
                ${percentage}%
              </span>
            </div>
          </div>
        `
      }
    },

    // 饼图系列配置
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: ['40%', '70%'], // 环形饼图
        center: ['40%', '50%'], // 左移为图例留空间
        avoidLabelOverlap: false,

        // 标签配置
        label: {
          show: true,
          position: 'outside',
          color: '#7BDEFF',
          fontSize: 11,
          fontFamily: 'Microsoft YaHei',
          formatter: (params: any) => {
            if (props.showPercentage) {
              return `${params.name}\n${params.percent}%`
            }
            return params.name
          }
        },

        // 标签线配置
        labelLine: {
          show: true,
          lineStyle: {
            color: '#7BDEFF',
            width: 1
          }
        },

        // 高亮样式
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(30, 144, 255, 0.5)'
          },
          label: {
            show: true,
            fontSize: 13,
            fontWeight: 'bold',
            color: '#ffffff'
          }
        },

        // 数据
        data: processedData,

        // 动画配置
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: () => Math.random() * 200,
        animationDuration: props.enableAnimation ? 1000 : 0
      }
    ]
  }

  // 设置配置并渲染
  chartInstance.value.setOption(option, true)
}

// 响应式调整图表大小
const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (newVal) => {
    if (!newVal) {
      nextTick(() => {
        updateChart()
      })
    }
  }
)

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法供父组件调用
defineExpose({
  resizeChart,
  updateChart,
  getChartInstance: () => chartInstance.value
})
</script>

<style scoped>
.labor-cost-pie-chart {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.labor-cost-pie-chart:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.4);
  border-color: #00CED1;
}

/* 图表标题 */
.chart-header {
  margin-bottom: 12px;
  text-align: center;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0 0 4px 0;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.3);
}

.chart-subtitle {
  font-size: 0.85rem;
  color: #7BDEFF;
  opacity: 0.8;
  margin: 0;
}

/* 图表容器 */
.chart-container {
  position: relative;
  min-height: 250px;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(14, 38, 92, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(30, 144, 255, 0.3);
  border-top: 3px solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: #7BDEFF;
  font-size: 0.9rem;
}

/* 无数据状态 */
.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #7BDEFF;
  opacity: 0.6;
}

.no-data-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.no-data-text {
  font-size: 0.9rem;
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .labor-cost-pie-chart {
    padding: 12px;
  }

  .chart-title {
    font-size: 1rem;
  }

  .chart-subtitle {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .labor-cost-pie-chart {
    padding: 8px;
  }

  .chart-container {
    min-height: 200px;
  }

  .chart-title {
    font-size: 0.9rem;
  }
}

/* 深色主题适配 */
.labor-cost-pie-chart {
  color: #ffffff;
}

/* 确保图表在容器中正确显示 */
.chart-container > div {
  width: 100% !important;
  height: 100% !important;
}
</style>
