{"name": "warehouse-dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "axios": "^1.10.0", "echarts": "^5.6.0", "gridstack": "^12.2.2", "pinia": "^3.0.3", "vue": "^3.5.17"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.14", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}