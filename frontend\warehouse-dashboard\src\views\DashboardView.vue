<template>
  <!-- 主容器改造为 Flex 布局 -->
  <div class="flex h-screen bg-gray-900 text-white">

    <!-- 左侧: 主画布区域 -->
    <div class="flex-1 flex flex-col overflow-auto">
      <!-- Header 部分 -->
      <header class="p-4 flex justify-between items-center bg-gray-800 border-b border-gray-700">
        <h1 class="text-xl font-bold text-white">
          <span class="text-blue-400">物流大数据</span>监控中心
        </h1>

        <!-- 筛选状态和清除按钮 -->
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-400">{{ filterStore.filterSummary }}</span>
          <button
            v-if="filterStore.hasFilter"
            @click="filterStore.clearFilter()"
            class="px-3 py-1 bg-red-500 hover:bg-red-600 rounded text-sm transition-colors"
          >
            清除筛选
          </button>
        </div>

        <div class="flex gap-2">
          <button
            @click="clearLayout"
            class="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 transition text-sm"
          >
            清空布局
          </button>
          <button
            @click="resetLayout"
            class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition text-sm"
          >
            重置布局
          </button>
        </div>
      </header>

      <!-- 全局筛选器 -->
      <div class="px-4 pt-2">
        <GlobalFilters />
      </div>

      <!-- 工具箱 -->
      <div class="px-4 py-2">
        <WidgetToolbox />
      </div>

      <!-- GridStack 容器 -->
      <div class="p-4 flex-1">
        <div
          ref="gridStackRef"
          class="grid-stack min-h-96"
          style="background: rgba(10, 22, 52, 0.1); border-radius: 8px; border: 1px solid rgba(0, 150, 255, 0.2);"
        >
          <!-- GridStack items will be dynamically added here -->
        </div>
      </div>
    </div>

    <!-- 右侧: 固定配置侧边栏 -->
    <div class="w-80 bg-gray-800 p-4 shadow-lg overflow-y-auto border-l border-gray-700">
      <WidgetConfigurator
        v-if="showConfigurator"
        :key="configuratorData.widgetId"
        :initial-props="configuratorData.initialProps"
        :component-type="configuratorData.componentType"
        @save="onConfigSave"
        @close="closeConfigurator"
      />
      <!-- 当没有组件被选中时显示提示信息 -->
      <div v-else class="text-center text-gray-400 mt-10">
        <svg class="mx-auto h-12 w-12 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-white">未选择组件</h3>
        <p class="mt-1 text-sm text-gray-400">请点击组件上的配置图标进行编辑。</p>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, createApp, computed } from 'vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'
import '@/assets/images/dashboard-bg.css'

import { useLayoutStore, type LayoutItem } from '@/stores/layout'
import WidgetToolbox from '@/components/WidgetToolbox.vue'
import WidgetConfigurator from '@/components/configurators/WidgetConfigurator.vue'
import GlobalFilters from '@/components/GlobalFilters.vue'
import { useFilterStore } from '@/stores/filterStore'

// 导入所有可能用到的卡片组件
import BasicInfoCard from '@/components/cards/BasicInfoCard.vue'
import SalesChartCard from '@/components/cards/SalesChartCard.vue'
import KeyMetricCard from '@/components/cards/KeyMetricCard.vue'
import MapCard from '@/components/cards/MapCard.vue'

const layoutStore = useLayoutStore()
const filterStore = useFilterStore()
const gridStackRef = ref<HTMLElement | null>(null)
let grid: GridStack | null = null
const componentInstances = new Map<string, any>()

// --- 逻辑重构开始 ---

// configuratorData 是新的核心状态，为 null 时表示面板为空
const configuratorData = ref<{
  widgetId: string
  initialProps: Record<string, any>
  componentType: string
} | null>(null)

// showConfigurator 现在是一个计算属性，用于 v-if 判断，使模板更清晰
const showConfigurator = computed(() => !!configuratorData.value)

// 组件映射表：将字符串名字映射到真实的组件对象
const componentMap = {
  BasicInfoCard,
  SalesChartCard,
  KeyMetricCard,
  MapCard
}

const getComponent = (componentName: string) => {
  return componentMap[componentName as keyof typeof componentMap] || null
}

// 创建GridStack项目的HTML结构
const createGridItem = (item: LayoutItem): HTMLElement => {
  const div = document.createElement('div')
  div.classList.add('grid-stack-item')
  div.setAttribute('gs-id', item.id)
  div.setAttribute('gs-x', item.x.toString())
  div.setAttribute('gs-y', item.y.toString())
  div.setAttribute('gs-w', item.w.toString())
  div.setAttribute('gs-h', item.h.toString())

  const wrapper = document.createElement('div')
  wrapper.classList.add('grid-stack-item-content-wrapper', 'relative', 'h-full')

  // 按钮容器
  const buttonContainer = document.createElement('div')
  buttonContainer.className = 'absolute top-1 right-1 z-10 flex space-x-1'

  // 设置按钮
  const settingsBtn = document.createElement('button')
  settingsBtn.innerHTML = '⚙️'
  settingsBtn.className = 'bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-70 hover:opacity-100 transition-opacity'
  settingsBtn.onclick = (e) => {
    e.stopPropagation()
    openConfigurator(item.id)
  }

  // 删除按钮
  const deleteBtn = document.createElement('button')
  deleteBtn.innerHTML = '×'
  deleteBtn.className = 'bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-70 hover:opacity-100 transition-opacity'
  deleteBtn.onclick = (e) => {
    e.stopPropagation()
    removeWidget(item.id)
  }

  buttonContainer.appendChild(settingsBtn)
  buttonContainer.appendChild(deleteBtn)

  const content = document.createElement('div')
  content.classList.add('grid-stack-item-content', 'h-full', 'rounded-lg', 'overflow-hidden')
  content.style.cssText = `
    background-color: rgba(10, 22, 52, 0.6);
    border: 1px solid rgba(0, 150, 255, 0.4);
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.2);
    backdrop-filter: blur(5px);
  `

  wrapper.appendChild(buttonContainer)
  wrapper.appendChild(content)
  div.appendChild(wrapper)
  return div
}

// 渲染Vue组件到GridStack项目中
const renderVueComponent = (item: LayoutItem, element: HTMLElement) => {
  const component = getComponent(item.component)
  if (!component) {
    console.warn(`Component ${item.component} not found`)
    return
  }

  const content = element.querySelector('.grid-stack-item-content')
  if (!content) return

  // 创建Vue应用实例，传递 widgetId
  const propsWithId = { ...item.props, widgetId: item.id }
  const app = createApp(component, propsWithId)
  const instance = app.mount(content)

  // 保存实例以便后续清理
  componentInstances.set(item.id, { app, instance })
}

// 移除组件
const removeWidget = (widgetId: string) => {
  // 清理Vue组件实例
  const instance = componentInstances.get(widgetId)
  if (instance) {
    instance.app.unmount()
    componentInstances.delete(widgetId)
  }

  // 从GridStack中移除
  if (grid) {
    const element = grid.getGridItems().find(el => el.getAttribute('gs-id') === widgetId)
    if (element) {
      grid.removeWidget(element)
    }
  }

  // 从store中移除
  layoutStore.removeWidget(widgetId)
}

// 初始化GridStack
const initGridStack = () => {
  console.log('Initializing GridStack...')
  console.log('gridStackRef.value:', gridStackRef.value)

  if (!gridStackRef.value) {
    console.error('GridStack container not found!')
    return
  }

  grid = GridStack.init({
    column: 12,
    cellHeight: 50,
    margin: 6,
    resizable: true,
    draggable: true,
    acceptWidgets: '.new-widget', // 接受来自工具箱的组件
    removable: false // 禁用默认的移除功能，我们用自定义删除按钮
  }, gridStackRef.value)

  console.log('GridStack initialized:', grid)

  // 监听从工具箱拖入新组件
  grid.on('added', (event, items) => {
    console.log('Widget added:', items)
    if (items && items.length > 0) {
      const newItem = items[0]
      const node = newItem.el as HTMLElement

      // 从 gs- attributes 恢复组件元数据
      const component = node.getAttribute('gs-component') || 'BasicInfoCard'
      const propsStr = node.getAttribute('gs-props') || '{}'
      let props = {}
      try {
        props = JSON.parse(propsStr)
      } catch (e) {
        console.warn('Failed to parse props:', propsStr)
      }

      // 添加到store
      layoutStore.addWidget({
        x: newItem.x || 0,
        y: newItem.y || 0,
        w: newItem.w || 4,
        h: newItem.h || 5,
        component,
        props
      })

      // 移除临时的占位符元素
      grid!.removeWidget(node, false)
    }
  })

  // 监听布局变化
  grid.on('change', (event, items) => {
    if (items && items.length > 0) {
      const newLayout: LayoutItem[] = items.map(item => {
        const existingItem = layoutStore.layout.find(l => l.id === item.id)
        return {
          id: item.id!,
          x: item.x!,
          y: item.y!,
          w: item.w!,
          h: item.h!,
          component: existingItem?.component || 'BasicInfoCard',
          props: existingItem?.props || {}
        }
      })
      layoutStore.updateLayout(newLayout)
    }
  })

  // 加载初始布局
  loadLayout()
}

// 加载布局
const loadLayout = () => {
  if (!grid) return

  // 清理现有组件
  componentInstances.forEach(({ app }) => {
    app.unmount()
  })
  componentInstances.clear()

  // 清空grid
  grid.removeAll()

  // 添加布局项
  layoutStore.layout.forEach(item => {
    const element = createGridItem(item)
    grid!.addWidget(element)
    renderVueComponent(item, element)
  })
}

// 清空布局
const clearLayout = () => {
  layoutStore.clearLayout()
  loadLayout()
}

// 重置布局
const resetLayout = () => {
  layoutStore.resetLayout()
  loadLayout()
}

/**
 * 当点击网格项的配置按钮时触发
 * @param {string} widgetId - 被点击的组件ID
 */
const openConfigurator = (widgetId: string) => {
  const widget = layoutStore.layout.find(w => w.id === widgetId)
  if (widget) {
    // 填充配置数据，这将触发侧边栏的显示和内容更新
    configuratorData.value = {
      widgetId: widgetId,
      initialProps: { ...widget.props }, // 使用展开运算符创建副本
      componentType: widget.component,
    }
  }
}

/**
 * 关闭配置侧边栏
 */
const closeConfigurator = () => {
  configuratorData.value = null // 清空数据即可关闭
}

/**
 * 当配置面板点击"应用更改"时触发
 * @param {object} newProps - 从配置器传回的新的属性对象
 */
const onConfigSave = (newProps: any) => {
  if (!configuratorData.value) return

  const widget = layoutStore.layout.find(w => w.id === configuratorData.value.widgetId)
  if (widget) {
    // 将新属性合并到现有属性中
    widget.props = { ...widget.props, ...newProps }

    // 重新渲染该组件
    const element = grid?.getGridItems().find(el => el.getAttribute('gs-id') === configuratorData.value!.widgetId)
    if (element) {
      renderVueComponent(widget, element)
    }
  }
  // 注意：我们在这里不自动关闭面板，允许用户连续调整
}

// --- 逻辑重构结束 ---

/**
 * 从工具箱添加一个新的组件
 * @param {string} componentType - 要添加的组件类型 (例如, 'SalesChartCard')
 */
const addWidget = (componentType: string) => {
  const newWidget = {
    id: `widget-${Date.now()}`,
    x: 0,
    y: layoutStore.layout.length, // 简单地堆叠在新行
    w: 6,
    h: 4,
    component: componentType,
    props: { // 提供一个合理的默认值
      title: '新组件',
      dataSourceId: null,
      chartType: 'bar' // 如果适用
    },
  }
  layoutStore.layout.push(newWidget)
  loadLayout()
}

// 组件挂载时加载布局
onMounted(() => {
  console.log('DashboardView mounted')
  console.log('Layout store data:', layoutStore.layout)
  initGridStack()
})

onUnmounted(() => {
  // 清理Vue组件实例
  componentInstances.forEach(({ app }) => {
    app.unmount()
  })
  componentInstances.clear()

  // 销毁GridStack实例
  if (grid) {
    grid.destroy()
  }
})
</script>

<style scoped>
.dashboard-view {
  font-family: 'Inter', sans-serif;
}

/* GridStack样式覆盖 */
:deep(.grid-stack) {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  min-height: 400px;
  padding: 16px;
}

:deep(.grid-stack-item) {
  background: transparent;
}

:deep(.grid-stack-item-content) {
  background: #374151;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

:deep(.grid-stack-item-content:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:deep(.grid-stack-item.ui-draggable-dragging) {
  opacity: 0.8;
  transform: rotate(1deg);
  z-index: 1000;
}

:deep(.grid-stack-item .ui-resizable-handle) {
  background: #3B82F6;
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.grid-stack-item:hover .ui-resizable-handle) {
  opacity: 0.7;
}

/* 操作按钮样式 */
:deep(.grid-stack-item-content-wrapper button) {
  font-family: Arial, sans-serif;
  font-weight: bold;
  line-height: 1;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

:deep(.grid-stack-item-content-wrapper button:hover) {
  transform: scale(1.1);
}

/* 按钮容器样式 */
:deep(.grid-stack-item-content-wrapper .flex) {
  opacity: 0;
  transition: opacity 0.2s ease;
}

:deep(.grid-stack-item:hover .grid-stack-item-content-wrapper .flex) {
  opacity: 1;
}

/* 拖拽占位符样式 */
:deep(.grid-stack-placeholder) {
  background: rgba(59, 130, 246, 0.3) !important;
  border: 2px dashed #3B82F6 !important;
  border-radius: 8px !important;
}

/* 外部拖拽时的样式 */
:deep(.grid-stack.ui-droppable-active) {
  background: rgba(34, 197, 94, 0.1) !important;
  border: 2px dashed #22C55E;
}
</style>
