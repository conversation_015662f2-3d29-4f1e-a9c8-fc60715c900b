<template>
  <div class="date-range-filter">
    <div class="filter-header">
      <div class="filter-title">
        <i class="fas fa-calendar-alt"></i>
        <span>日期范围筛选</span>
      </div>
      <div class="filter-actions">
        <button 
          class="reset-btn" 
          @click="resetDateRange"
          :disabled="!startDate && !endDate"
        >
          <i class="fas fa-undo"></i>
          重置
        </button>
      </div>
    </div>
    
    <div class="filter-content">
      <div class="date-input-group">
        <div class="date-input-item">
          <label for="start-date">开始日期</label>
          <input
            id="start-date"
            type="date"
            v-model="startDate"
            :max="endDate || maxDate"
            class="date-input"
          />
        </div>
        
        <div class="date-separator">
          <i class="fas fa-arrow-right"></i>
        </div>
        
        <div class="date-input-item">
          <label for="end-date">结束日期</label>
          <input
            id="end-date"
            type="date"
            v-model="endDate"
            :min="startDate || minDate"
            :max="maxDate"
            class="date-input"
          />
        </div>
      </div>
      
      <div class="filter-info" v-if="dateRangeInfo">
        <i class="fas fa-info-circle"></i>
        <span>{{ dateRangeInfo }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { DateRange } from '../types/index'

// 定义 emit 事件
const emit = defineEmits<{
  'update:dateRange': [dateRange: DateRange]
}>()

// 响应式变量
const startDate = ref<string>('')
const endDate = ref<string>('')

// 计算属性：日期范围限制
const minDate = computed(() => {
  // 设置最小日期为一年前
  const date = new Date()
  date.setFullYear(date.getFullYear() - 1)
  return date.toISOString().split('T')[0]
})

const maxDate = computed(() => {
  // 设置最大日期为今天
  return new Date().toISOString().split('T')[0]
})

// 计算属性：日期范围信息
const dateRangeInfo = computed(() => {
  if (!startDate.value && !endDate.value) {
    return '请选择日期范围以筛选数据'
  }
  
  if (startDate.value && endDate.value) {
    const start = new Date(startDate.value)
    const end = new Date(endDate.value)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return `已选择 ${diffDays + 1} 天的数据范围`
  }
  
  if (startDate.value) {
    return `从 ${formatDate(startDate.value)} 开始`
  }
  
  if (endDate.value) {
    return `到 ${formatDate(endDate.value)} 结束`
  }
  
  return ''
})

// 工具函数：格式化日期显示
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 重置日期范围
const resetDateRange = () => {
  startDate.value = ''
  endDate.value = ''
}

// 监听日期变化并发出事件
watch([startDate, endDate], ([newStart, newEnd]) => {
  const dateRange: DateRange = {
    start: newStart || null,
    end: newEnd || null
  }
  
  emit('update:dateRange', dateRange)
}, { immediate: true })

// 验证日期范围的合理性
watch([startDate, endDate], ([newStart, newEnd]) => {
  if (newStart && newEnd) {
    const start = new Date(newStart)
    const end = new Date(newEnd)
    
    if (start > end) {
      // 如果开始日期晚于结束日期，自动调整
      endDate.value = newStart
    }
  }
})
</script>

<style scoped>
.date-range-filter {
  background: rgba(14, 38, 92, 0.8);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7BDEFF;
  font-size: 1rem;
  font-weight: 600;
}

.filter-title i {
  color: #1E90FF;
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
}

.reset-btn {
  background: rgba(255, 107, 107, 0.8);
  color: #ffffff;
  border: 1px solid #FF6B6B;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.reset-btn:hover:not(:disabled) {
  background: rgba(255, 107, 107, 1);
  transform: translateY(-1px);
}

.reset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.date-input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.date-input-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.date-input-item label {
  color: #7BDEFF;
  font-size: 0.9rem;
  font-weight: 500;
}

.date-input {
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 4px;
  padding: 0.5rem;
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.date-input:focus {
  outline: none;
  border-color: #1E90FF;
  background: rgba(30, 144, 255, 0.2);
  box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.2);
}

.date-input::-webkit-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}

.date-separator {
  color: #7BDEFF;
  font-size: 1.2rem;
  margin: 0 0.5rem;
  align-self: flex-end;
  margin-bottom: 0.5rem;
}

.filter-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #FFD700;
  font-size: 0.85rem;
  padding: 0.5rem;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 4px;
}

.filter-info i {
  color: #FFD700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .date-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-separator {
    align-self: center;
    margin: 0;
    transform: rotate(90deg);
  }
  
  .date-input-item {
    min-width: auto;
    width: 100%;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .date-input {
    color-scheme: dark;
  }
}
</style>
