<template>
  <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200 transition-all duration-300 hover:shadow-lg">
    <!-- 图表标题 -->
    <div class="chart-header mb-4">
      <h3 class="text-lg font-semibold text-slate-800 mb-2">{{ title }}</h3>
      <div class="text-sm text-slate-600" v-if="subtitle">{{ subtitle }}</div>
    </div>
    
    <!-- ECharts 容器 -->
    <div 
      ref="chartContainer" 
      class="chart-container"
      :style="{ width: '100%', height: chartHeight }"
    ></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
    
    <!-- 无数据状态 -->
    <div v-if="!loading && (!chartData || !chartData.xAxisData || chartData.xAxisData.length === 0)" class="no-data">
      <div class="no-data-icon">📈</div>
      <div class="no-data-text">暂无数据</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props 定义
const props = defineProps({
  // 图表数据
  chartData: {
    type: Object,
    default: () => ({
      xAxisData: [],
      seriesData: []
    }),
    validator: (value) => {
      return (
        typeof value === 'object' &&
        Array.isArray(value.xAxisData) &&
        Array.isArray(value.seriesData) &&
        value.xAxisData.length === value.seriesData.length
      )
    }
  },
  
  // 图表标题
  title: {
    type: String,
    default: '月度订单趋势'
  },
  
  // 图表副标题
  subtitle: {
    type: String,
    default: ''
  },
  
  // 图表高度
  chartHeight: {
    type: String,
    default: '300px'
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  
  // 是否显示数值标签
  showLabel: {
    type: Boolean,
    default: true
  },
  
  // 是否启用动画
  enableAnimation: {
    type: Boolean,
    default: true
  },
  
  // 主题色
  themeColor: {
    type: String,
    default: '#00CED1' // 青色
  }
})

// 响应式数据
const chartContainer = ref(null)
const chartInstance = ref(null)

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  // 销毁已存在的实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  
  // 创建新的图表实例
  chartInstance.value = echarts.init(chartContainer.value)
  
  // 设置图表配置
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance.value || !props.chartData || !props.chartData.xAxisData || props.chartData.xAxisData.length === 0) {
    return
  }
  
  const option = {
    // 背景透明
    backgroundColor: 'transparent',
    
    // 网格配置
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    
    // 提示框配置
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(14, 38, 92, 0.9)',
      borderColor: props.themeColor,
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: props.themeColor,
          width: 1,
          opacity: 0.6
        },
        crossStyle: {
          color: props.themeColor
        }
      },
      formatter: (params) => {
        const data = params[0]
        return `
          <div style="padding: 8px;">
            <div style="color: #7BDEFF; font-weight: bold; margin-bottom: 4px;">
              ${data.name}
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 10px; height: 10px; background: ${props.themeColor}; border-radius: 50%;"></div>
              <span>订单量:</span>
              <span style="color: #00FF7F; font-weight: bold;">
                ${data.value.toLocaleString()} 单
              </span>
            </div>
          </div>
        `
      }
    },
    
    // X轴配置
    xAxis: {
      type: 'category',
      data: props.chartData.xAxisData,
      axisLine: {
        lineStyle: {
          color: '#a9c1d9',
          width: 1
        }
      },
      axisTick: {
        lineStyle: {
          color: '#a9c1d9'
        }
      },
      axisLabel: {
        color: '#a9c1d9',
        fontSize: 11,
        fontFamily: 'Microsoft YaHei'
      },
      splitLine: {
        show: false
      }
    },
    
    // Y轴配置
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#a9c1d9',
          width: 1
        }
      },
      axisTick: {
        lineStyle: {
          color: '#a9c1d9'
        }
      },
      axisLabel: {
        color: '#a9c1d9',
        fontSize: 11,
        fontFamily: 'Microsoft YaHei',
        formatter: (value) => {
          if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'k'
          }
          return value
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(169, 193, 217, 0.1)',
          width: 1,
          type: 'dashed'
        }
      }
    },
    
    // 系列配置
    series: [
      {
        name: '订单量',
        type: 'line',
        data: props.chartData.seriesData,
        smooth: true, // 平滑曲线
        
        // 线条样式
        lineStyle: {
          color: props.themeColor,
          width: 3,
          shadowColor: props.themeColor,
          shadowBlur: 10,
          shadowOffsetY: 3
        },
        
        // 数据点样式
        itemStyle: {
          color: props.themeColor,
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: props.themeColor,
          shadowBlur: 8
        },
        
        // 高亮样式
        emphasis: {
          itemStyle: {
            color: '#ffffff',
            borderColor: props.themeColor,
            borderWidth: 3,
            shadowColor: props.themeColor,
            shadowBlur: 15,
            scale: 1.2
          }
        },
        
        // 数值标签
        label: {
          show: props.showLabel,
          position: 'top',
          color: '#7BDEFF',
          fontSize: 10,
          fontFamily: 'Microsoft YaHei',
          formatter: (params) => {
            if (params.value >= 1000) {
              return (params.value / 1000).toFixed(1) + 'k'
            }
            return params.value
          }
        },
        
        // 面积填充
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: `rgba(${hexToRgb(props.themeColor)}, 0.6)`
            },
            {
              offset: 0.5,
              color: `rgba(${hexToRgb(props.themeColor)}, 0.3)`
            },
            {
              offset: 1,
              color: `rgba(${hexToRgb(props.themeColor)}, 0)`
            }
          ]),
          shadowColor: props.themeColor,
          shadowBlur: 20
        },
        
        // 动画配置
        animationDuration: props.enableAnimation ? 2000 : 0,
        animationEasing: 'cubicOut',
        animationDelay: (idx) => idx * 100
      }
    ]
  }
  
  // 设置配置并渲染
  chartInstance.value.setOption(option, true)
}

// 颜色转换工具函数
const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  if (result) {
    return `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
  }
  return '0, 206, 209' // 默认青色
}

// 响应式调整图表大小
const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (newVal) => {
    if (!newVal) {
      nextTick(() => {
        updateChart()
      })
    }
  }
)

// 监听主题色变化
watch(
  () => props.themeColor,
  () => {
    nextTick(() => {
      updateChart()
    })
  }
)

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法供父组件调用
defineExpose({
  resizeChart,
  updateChart,
  getChartInstance: () => chartInstance.value
})
</script>

<style scoped>
.monthly-orders-chart {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.monthly-orders-chart:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.4);
  border-color: #00CED1;
}

/* 图表标题 */
.chart-header {
  margin-bottom: 12px;
  text-align: center;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0 0 4px 0;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.3);
}

.chart-subtitle {
  font-size: 0.85rem;
  color: #7BDEFF;
  opacity: 0.8;
  margin: 0;
}

/* 图表容器 */
.chart-container {
  position: relative;
  min-height: 250px;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(14, 38, 92, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(30, 144, 255, 0.3);
  border-top: 3px solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: #7BDEFF;
  font-size: 0.9rem;
}

/* 无数据状态 */
.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #7BDEFF;
  opacity: 0.6;
}

.no-data-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.no-data-text {
  font-size: 0.9rem;
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .monthly-orders-chart {
    padding: 12px;
  }
  
  .chart-title {
    font-size: 1rem;
  }
  
  .chart-subtitle {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .monthly-orders-chart {
    padding: 8px;
  }
  
  .chart-container {
    min-height: 200px;
  }
  
  .chart-title {
    font-size: 0.9rem;
  }
}

/* 深色主题适配 */
.monthly-orders-chart {
  color: #ffffff;
}

/* 确保图表在容器中正确显示 */
.chart-container > div {
  width: 100% !important;
  height: 100% !important;
}
</style>
