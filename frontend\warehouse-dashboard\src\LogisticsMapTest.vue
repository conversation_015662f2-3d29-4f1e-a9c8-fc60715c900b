<template>
  <div class="logistics-map-test">
    <AppHeader title="物流网络地图组件测试" />
    
    <div class="test-container">
      <div class="controls-section">
        <h2>控制面板</h2>
        <div class="controls-grid">
          <div class="control-group">
            <label>数据集选择:</label>
            <select v-model="selectedDataset" @change="switchDataset">
              <option value="default">默认网络</option>
              <option value="expanded">扩展网络</option>
              <option value="minimal">精简网络</option>
              <option value="custom">自定义网络</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="enableAnimation" />
              启用动画效果
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="isLoading" />
              加载状态
            </label>
          </div>
          
          <div class="control-group">
            <button @click="refreshData" class="refresh-btn">
              刷新数据
            </button>
          </div>
          
          <div class="control-group">
            <button @click="addRandomRoute" class="add-btn">
              添加随机路线
            </button>
          </div>
          
          <div class="control-group">
            <button @click="simulateTraffic" class="traffic-btn">
              模拟流量变化
            </button>
          </div>
        </div>
      </div>

      <div class="maps-section">
        <h2>地图展示</h2>
        <div class="maps-grid">
          <!-- 标准尺寸地图 -->
          <div class="map-wrapper">
            <h3>标准尺寸 (800x500)</h3>
            <LogisticsMap
              :warehouseData="currentWarehouseData"
              :routeData="currentRouteData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :enableAnimation="enableAnimation"
              mapHeight="500px"
            />
          </div>
          
          <!-- 大尺寸地图 -->
          <div class="map-wrapper large">
            <h3>大尺寸 (1200x600)</h3>
            <LogisticsMap
              :warehouseData="currentWarehouseData"
              :routeData="currentRouteData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :enableAnimation="enableAnimation"
              mapHeight="600px"
            />
          </div>
          
          <!-- 实时数据地图 -->
          <div class="map-wrapper">
            <h3>实时数据更新</h3>
            <LogisticsMap
              :warehouseData="realtimeWarehouseData"
              :routeData="realtimeRouteData"
              title="实时物流网络"
              subtitle="数据每10秒自动更新"
              :loading="false"
              :enableAnimation="true"
              mapHeight="500px"
            />
          </div>
        </div>
      </div>

      <div class="data-section">
        <h2>当前数据结构</h2>
        <div class="data-display">
          <div class="data-item">
            <h4>仓库数据 (warehouseData):</h4>
            <pre>{{ JSON.stringify(currentWarehouseData.slice(0, 3), null, 2) }}...</pre>
          </div>
          <div class="data-item">
            <h4>路线数据 (routeData):</h4>
            <pre>{{ JSON.stringify(currentRouteData.slice(0, 3), null, 2) }}...</pre>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <h2>网络统计</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ currentWarehouseData.length }}</div>
            <div class="stat-label">仓库节点</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ currentRouteData.length }}</div>
            <div class="stat-label">运输路线</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ totalBusinessVolume }}</div>
            <div class="stat-label">总业务量</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ networkCoverage }}%</div>
            <div class="stat-label">网络覆盖率</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppHeader from './components/AppHeader.vue'
import LogisticsMap from './components/LogisticsMap.vue'

// 响应式数据
const selectedDataset = ref('default')
const enableAnimation = ref(true)
const isLoading = ref(false)

// 预设数据集
const datasets = {
  default: {
    title: '全国物流网络',
    subtitle: '主要城市仓库分布与运输路线',
    warehouses: [
      { name: '北京', value: [116.40, 39.90, 150] },
      { name: '上海', value: [121.47, 31.23, 200] },
      { name: '广州', value: [113.23, 23.16, 180] },
      { name: '深圳', value: [114.07, 22.62, 220] },
      { name: '武汉', value: [114.31, 30.52, 120] },
      { name: '成都', value: [104.06, 30.67, 160] },
      { name: '西安', value: [108.95, 34.27, 100] },
      { name: '杭州', value: [120.19, 30.26, 140] }
    ],
    routes: [
      { fromName: '北京', toName: '上海', coords: [[116.40, 39.90], [121.47, 31.23]] },
      { fromName: '上海', toName: '广州', coords: [[121.47, 31.23], [113.23, 23.16]] },
      { fromName: '广州', toName: '深圳', coords: [[113.23, 23.16], [114.07, 22.62]] },
      { fromName: '北京', toName: '武汉', coords: [[116.40, 39.90], [114.31, 30.52]] },
      { fromName: '武汉', toName: '成都', coords: [[114.31, 30.52], [104.06, 30.67]] },
      { fromName: '北京', toName: '西安', coords: [[116.40, 39.90], [108.95, 34.27]] },
      { fromName: '上海', toName: '杭州', coords: [[121.47, 31.23], [120.19, 30.26]] }
    ]
  },
  expanded: {
    title: '扩展物流网络',
    subtitle: '覆盖更多二三线城市',
    warehouses: [
      { name: '北京', value: [116.40, 39.90, 150] },
      { name: '上海', value: [121.47, 31.23, 200] },
      { name: '广州', value: [113.23, 23.16, 180] },
      { name: '深圳', value: [114.07, 22.62, 220] },
      { name: '武汉', value: [114.31, 30.52, 120] },
      { name: '成都', value: [104.06, 30.67, 160] },
      { name: '西安', value: [108.95, 34.27, 100] },
      { name: '杭州', value: [120.19, 30.26, 140] },
      { name: '南京', value: [118.78, 32.04, 110] },
      { name: '天津', value: [117.20, 39.13, 90] },
      { name: '重庆', value: [106.54, 29.59, 130] },
      { name: '青岛', value: [120.33, 36.07, 85] }
    ],
    routes: [
      { fromName: '北京', toName: '上海', coords: [[116.40, 39.90], [121.47, 31.23]] },
      { fromName: '上海', toName: '广州', coords: [[121.47, 31.23], [113.23, 23.16]] },
      { fromName: '广州', toName: '深圳', coords: [[113.23, 23.16], [114.07, 22.62]] },
      { fromName: '北京', toName: '武汉', coords: [[116.40, 39.90], [114.31, 30.52]] },
      { fromName: '武汉', toName: '成都', coords: [[114.31, 30.52], [104.06, 30.67]] },
      { fromName: '北京', toName: '西安', coords: [[116.40, 39.90], [108.95, 34.27]] },
      { fromName: '上海', toName: '杭州', coords: [[121.47, 31.23], [120.19, 30.26]] },
      { fromName: '上海', toName: '南京', coords: [[121.47, 31.23], [118.78, 32.04]] },
      { fromName: '北京', toName: '天津', coords: [[116.40, 39.90], [117.20, 39.13]] },
      { fromName: '成都', toName: '重庆', coords: [[104.06, 30.67], [106.54, 29.59]] },
      { fromName: '北京', toName: '青岛', coords: [[116.40, 39.90], [120.33, 36.07]] }
    ]
  },
  minimal: {
    title: '核心物流网络',
    subtitle: '一线城市核心节点',
    warehouses: [
      { name: '北京', value: [116.40, 39.90, 200] },
      { name: '上海', value: [121.47, 31.23, 250] },
      { name: '广州', value: [113.23, 23.16, 220] },
      { name: '深圳', value: [114.07, 22.62, 280] }
    ],
    routes: [
      { fromName: '北京', toName: '上海', coords: [[116.40, 39.90], [121.47, 31.23]] },
      { fromName: '上海', toName: '广州', coords: [[121.47, 31.23], [113.23, 23.16]] },
      { fromName: '广州', toName: '深圳', coords: [[113.23, 23.16], [114.07, 22.62]] }
    ]
  },
  custom: {
    title: '自定义网络',
    subtitle: '用户定制化配置',
    warehouses: [
      { name: '北京', value: [116.40, 39.90, 180] },
      { name: '上海', value: [121.47, 31.23, 220] },
      { name: '武汉', value: [114.31, 30.52, 140] },
      { name: '成都', value: [104.06, 30.67, 160] },
      { name: '西安', value: [108.95, 34.27, 120] }
    ],
    routes: [
      { fromName: '北京', toName: '武汉', coords: [[116.40, 39.90], [114.31, 30.52]] },
      { fromName: '武汉', toName: '成都', coords: [[114.31, 30.52], [104.06, 30.67]] },
      { fromName: '武汉', toName: '上海', coords: [[114.31, 30.52], [121.47, 31.23]] },
      { fromName: '西安', toName: '成都', coords: [[108.95, 34.27], [104.06, 30.67]] }
    ]
  }
}

// 实时数据（模拟动态更新）
const realtimeWarehouseData = ref([
  { name: '北京', value: [116.40, 39.90, 150] },
  { name: '上海', value: [121.47, 31.23, 200] },
  { name: '广州', value: [113.23, 23.16, 180] },
  { name: '武汉', value: [114.31, 30.52, 120] }
])

const realtimeRouteData = ref([
  { fromName: '北京', toName: '上海', coords: [[116.40, 39.90], [121.47, 31.23]] },
  { fromName: '上海', toName: '广州', coords: [[121.47, 31.23], [113.23, 23.16]] },
  { fromName: '北京', toName: '武汉', coords: [[116.40, 39.90], [114.31, 30.52]] }
])

// 当前数据
const currentWarehouseData = computed(() => datasets[selectedDataset.value].warehouses)
const currentRouteData = computed(() => datasets[selectedDataset.value].routes)
const currentTitle = computed(() => datasets[selectedDataset.value].title)
const currentSubtitle = computed(() => datasets[selectedDataset.value].subtitle)

// 统计数据
const totalBusinessVolume = computed(() => {
  return currentWarehouseData.value.reduce((sum, item) => sum + item.value[2], 0)
})

const networkCoverage = computed(() => {
  return Math.round((currentWarehouseData.value.length / 34) * 100) // 假设全国有34个主要城市
})

// 切换数据集
const switchDataset = () => {
  console.log('切换到数据集:', selectedDataset.value)
}

// 刷新数据
const refreshData = () => {
  const currentDataset = datasets[selectedDataset.value]
  currentDataset.warehouses.forEach(warehouse => {
    // 随机变化业务量 ±20%
    const change = (Math.random() - 0.5) * 0.4
    warehouse.value[2] = Math.round(warehouse.value[2] * (1 + change))
  })
  console.log('数据已刷新')
}

// 添加随机路线
const addRandomRoute = () => {
  const warehouses = currentWarehouseData.value
  if (warehouses.length < 2) return
  
  const from = warehouses[Math.floor(Math.random() * warehouses.length)]
  const to = warehouses[Math.floor(Math.random() * warehouses.length)]
  
  if (from.name !== to.name) {
    const newRoute = {
      fromName: from.name,
      toName: to.name,
      coords: [[from.value[0], from.value[1]], [to.value[0], to.value[1]]]
    }
    
    datasets[selectedDataset.value].routes.push(newRoute)
    console.log('添加新路线:', newRoute.fromName, '->', newRoute.toName)
  }
}

// 模拟流量变化
const simulateTraffic = () => {
  realtimeWarehouseData.value.forEach(warehouse => {
    const change = (Math.random() - 0.5) * 0.3
    warehouse.value[2] = Math.round(warehouse.value[2] * (1 + change))
  })
  console.log('流量数据已更新')
}

// 实时数据更新
const updateRealtimeData = () => {
  realtimeWarehouseData.value.forEach(warehouse => {
    const change = (Math.random() - 0.5) * 0.1
    warehouse.value[2] = Math.round(warehouse.value[2] * (1 + change))
  })
}

let realtimeInterval = null

onMounted(() => {
  // 启动实时数据更新
  realtimeInterval = setInterval(updateRealtimeData, 10000)
  console.log('物流网络地图测试页面已加载')
})

onUnmounted(() => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
  }
})
</script>

<style scoped>
.logistics-map-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
  max-width: 1800px;
  margin: 0 auto;
}

.controls-section,
.maps-section,
.data-section,
.stats-section {
  margin-bottom: 40px;
}

.controls-section h2,
.maps-section h2,
.data-section h2,
.stats-section h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  background: rgba(14, 38, 92, 0.6);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #1E90FF;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  color: #7BDEFF;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group select {
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid #1E90FF;
  color: #ffffff;
  padding: 8px;
  border-radius: 4px;
}

.control-group input[type="checkbox"] {
  accent-color: #1E90FF;
}

.refresh-btn,
.add-btn,
.traffic-btn {
  background: rgba(0, 255, 127, 0.8);
  color: #ffffff;
  border: 1px solid #00FF7F;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.add-btn {
  background: rgba(255, 215, 0, 0.8);
  border-color: #FFD700;
}

.traffic-btn {
  background: rgba(30, 144, 255, 0.8);
  border-color: #1E90FF;
}

.refresh-btn:hover,
.add-btn:hover,
.traffic-btn:hover {
  transform: translateY(-2px);
  opacity: 1;
}

.maps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
  gap: 20px;
}

.map-wrapper {
  background: rgba(14, 38, 92, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  min-height: 600px;
}

.map-wrapper.large {
  grid-column: span 2;
  min-height: 700px;
}

.map-wrapper h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1rem;
}

.data-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.data-item {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
}

.data-item h4 {
  color: #7BDEFF;
  margin-bottom: 10px;
  font-size: 1rem;
}

.data-item pre {
  color: #7BDEFF;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  margin: 0;
  white-space: pre-wrap;
  overflow-x: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.3);
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #00FF7F;
  margin-bottom: 8px;
}

.stat-label {
  color: #7BDEFF;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .maps-grid {
    grid-template-columns: 1fr;
  }
  
  .map-wrapper.large {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .controls-grid {
    grid-template-columns: 1fr;
  }
  
  .data-display {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
