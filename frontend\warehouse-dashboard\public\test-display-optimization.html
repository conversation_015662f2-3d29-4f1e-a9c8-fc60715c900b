<!DOCTYPE html>
<html>
<head>
    <title>字体和图形显示优化测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.6.0/dist/echarts.min.js"></script>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif;
            background: #1a1a2e; 
            color: white; 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #40e0d0; 
            border-radius: 5px; 
            background: #16213e; 
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: rgba(16, 33, 62, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            height: 250px;
        }
        .test-title {
            font-size: 1rem;
            font-weight: 600;
            color: #7BDEFF;
            margin-bottom: 12px;
            text-shadow: 0 0 10px rgba(123, 222, 255, 0.5);
        }
        .chart-container {
            width: 100%;
            height: 200px;
        }
        .font-test {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }
        .metric-display {
            text-align: center;
            padding: 20px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00FF88;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
        }
        .metric-label {
            font-size: 0.9rem;
            color: #7BDEFF;
            margin-top: 8px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <h1>🎨 字体和图形显示优化测试</h1>
    
    <div class="test-section">
        <h2>字体显示测试</h2>
        <div class="font-test">
            <h3>中文字体测试</h3>
            <p><strong>常用汉字：</strong>物流大数据展示平台 - 仓储管理系统</p>
            <p><strong>数字显示：</strong>12,580 个 | 8,960 万元 | 156.8 万吨 | 23 天</p>
            <p><strong>特殊字符：</strong>TOC业务量 ↗ 2.1% | TOB业务量 ↘ 1.8% | 客户满意度 ★★★★☆</p>
            <p><strong>英文混合：</strong>Dashboard Analytics | KPI Metrics | Real-time Data</p>
        </div>
        
        <div class="font-test">
            <h3>不同字号测试</h3>
            <div style="font-size: 0.7rem;">小字号 (0.7rem): 详细信息和备注文字</div>
            <div style="font-size: 0.9rem;">标准字号 (0.9rem): 标签和说明文字</div>
            <div style="font-size: 1.2rem;">中等字号 (1.2rem): 标题和重要信息</div>
            <div style="font-size: 1.6rem;">大字号 (1.6rem): 主要数值显示</div>
            <div style="font-size: 2rem;">超大字号 (2rem): 关键指标数值</div>
        </div>
    </div>

    <div class="test-section">
        <h2>数值显示测试</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">TOC业务量</div>
                <div class="metric-display">
                    <div class="metric-value">12,580</div>
                    <div class="metric-label">个</div>
                    <div style="color: #00FF88; margin-top: 8px;">↗ +2.1% 同比增长</div>
                </div>
            </div>
            
            <div class="test-card">
                <div class="test-title">TOB业务量</div>
                <div class="metric-display">
                    <div class="metric-value">8,960</div>
                    <div class="metric-label">万元</div>
                    <div style="color: #FF6B6B; margin-top: 8px;">↘ -1.8% 同比下降</div>
                </div>
            </div>
            
            <div class="test-card">
                <div class="test-title">货物总量</div>
                <div class="metric-display">
                    <div class="metric-value">156.8</div>
                    <div class="metric-label">万吨</div>
                    <div style="color: #FFD700; margin-top: 8px;">→ 0% 持平</div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>图表显示测试</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">折线图测试</div>
                <div id="lineChart" class="chart-container"></div>
            </div>
            
            <div class="test-card">
                <div class="test-title">柱状图测试</div>
                <div id="barChart" class="chart-container"></div>
            </div>
            
            <div class="test-card">
                <div class="test-title">饼图测试</div>
                <div id="pieChart" class="chart-container"></div>
            </div>
            
            <div class="test-card">
                <div class="test-title">仪表盘测试</div>
                <div id="gaugeChart" class="chart-container"></div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>响应式测试指南</h2>
        <div class="info">
            <h3>🔍 测试步骤</h3>
            <ol>
                <li><strong>字体清晰度</strong>: 检查中文字符是否清晰显示，无乱码或重叠</li>
                <li><strong>数值对齐</strong>: 确认数字和单位正确对齐，无截断</li>
                <li><strong>图表文字</strong>: 验证图表中的标签、图例、坐标轴文字是否清晰</li>
                <li><strong>响应式适配</strong>: 调整浏览器窗口大小，观察文字和图表的适配效果</li>
                <li><strong>不同缩放</strong>: 测试浏览器缩放（Ctrl + / Ctrl -）下的显示效果</li>
            </ol>
        </div>
        
        <div class="warning">
            <h3>⚠️ 常见问题检查</h3>
            <ul>
                <li>中文字体是否正确加载（不应显示为方块或问号）</li>
                <li>数值是否完整显示（不应被截断或重叠）</li>
                <li>图表标签是否清晰可读</li>
                <li>在小屏幕下内容是否仍然可见</li>
                <li>高DPI屏幕下是否清晰</li>
            </ul>
        </div>
    </div>

    <script>
        // 通用字体配置
        const fontFamily = '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif';
        
        // 折线图
        const lineChart = echarts.init(document.getElementById('lineChart'));
        lineChart.setOption({
            title: {
                text: '月度趋势',
                textStyle: { color: '#7BDEFF', fontSize: 12, fontFamily }
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(10, 22, 52, 0.9)',
                textStyle: { color: '#FFFFFF', fontFamily }
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月'],
                axisLabel: { color: '#FFFFFF', fontSize: 10, fontFamily }
            },
            yAxis: {
                type: 'value',
                axisLabel: { color: '#FFFFFF', fontSize: 10, fontFamily }
            },
            series: [{
                type: 'line',
                data: [2100, 1800, 2500, 3200, 3000, 2800],
                lineStyle: { color: '#00BFFF' },
                itemStyle: { color: '#00BFFF' }
            }]
        });

        // 柱状图
        const barChart = echarts.init(document.getElementById('barChart'));
        barChart.setOption({
            title: {
                text: '区域分布',
                textStyle: { color: '#7BDEFF', fontSize: 12, fontFamily }
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(10, 22, 52, 0.9)',
                textStyle: { color: '#FFFFFF', fontFamily }
            },
            xAxis: {
                type: 'category',
                data: ['武汉', '黄冈', '天门', '深圳'],
                axisLabel: { color: '#FFFFFF', fontSize: 10, fontFamily }
            },
            yAxis: {
                type: 'value',
                axisLabel: { color: '#FFFFFF', fontSize: 10, fontFamily }
            },
            series: [{
                type: 'bar',
                data: [3500, 2800, 2200, 4100],
                itemStyle: { color: '#32CD32' }
            }]
        });

        // 饼图
        const pieChart = echarts.init(document.getElementById('pieChart'));
        pieChart.setOption({
            title: {
                text: '业务占比',
                textStyle: { color: '#7BDEFF', fontSize: 12, fontFamily }
            },
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(10, 22, 52, 0.9)',
                textStyle: { color: '#FFFFFF', fontFamily }
            },
            legend: {
                bottom: '5%',
                textStyle: { color: '#FFFFFF', fontSize: 10, fontFamily }
            },
            series: [{
                type: 'pie',
                radius: ['30%', '70%'],
                data: [
                    { value: 35, name: 'TOC业务' },
                    { value: 45, name: 'TOB业务' },
                    { value: 20, name: '其他业务' }
                ],
                label: {
                    color: '#FFFFFF',
                    fontSize: 10,
                    fontFamily
                }
            }]
        });

        // 仪表盘
        const gaugeChart = echarts.init(document.getElementById('gaugeChart'));
        gaugeChart.setOption({
            title: {
                text: '完成率',
                textStyle: { color: '#7BDEFF', fontSize: 12, fontFamily }
            },
            series: [{
                type: 'gauge',
                detail: {
                    valueAnimation: true,
                    formatter: '{value}%',
                    color: '#00BFFF',
                    fontSize: 16,
                    fontFamily
                },
                data: [{ value: 78, name: '目标完成' }],
                axisLabel: {
                    color: '#FFFFFF',
                    fontSize: 10,
                    fontFamily
                },
                title: {
                    color: '#FFFFFF',
                    fontSize: 12,
                    fontFamily
                }
            }]
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            lineChart.resize();
            barChart.resize();
            pieChart.resize();
            gaugeChart.resize();
        });

        console.log('✅ 字体和图形显示优化测试页面加载完成');
        console.log('📝 请检查页面中的字体显示和图表渲染效果');
    </script>
</body>
</html>
