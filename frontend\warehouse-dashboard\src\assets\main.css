@import './base.css';
@import './chart-optimization.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  #app {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
  }
}
