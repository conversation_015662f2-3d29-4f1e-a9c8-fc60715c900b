/* 图表组件显示优化样式 */

/* 全局字体优化 */
* {
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif !important;
}

/* 图表容器优化 */
.chart-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 200px;
  position: relative;
}

/* ECharts 画布优化 */
.echarts-container canvas {
  width: 100% !important;
  height: 100% !important;
}

/* 卡片组件优化 */
.card-component {
  overflow: hidden;
  border-radius: 8px;
  background: rgba(16, 33, 62, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin-bottom: 12px;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.5);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-content {
  height: calc(100% - 40px);
  overflow: hidden;
}

/* 数值显示优化 */
.metric-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #00FF88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
  line-height: 1.2;
}

.metric-label {
  font-size: 0.9rem;
  color: #7BDEFF;
  font-weight: 500;
  margin-bottom: 8px;
}

.metric-unit {
  font-size: 0.8rem;
  color: #B3E5FC;
  margin-left: 4px;
}

/* 趋势指示器优化 */
.trend-positive {
  color: #00FF88;
}

.trend-negative {
  color: #FF6B6B;
}

.trend-neutral {
  color: #FFD700;
}

/* 响应式字体大小 */
@media (max-width: 1920px) {
  .metric-value { font-size: 1.6rem; }
  .card-title { font-size: 0.95rem; }
  .metric-label { font-size: 0.85rem; }
}

@media (max-width: 1600px) {
  .metric-value { font-size: 1.4rem; }
  .card-title { font-size: 0.9rem; }
  .metric-label { font-size: 0.8rem; }
}

@media (max-width: 1280px) {
  .metric-value { font-size: 1.2rem; }
  .card-title { font-size: 0.85rem; }
  .metric-label { font-size: 0.75rem; }
}

@media (max-width: 1024px) {
  .metric-value { font-size: 1rem; }
  .card-title { font-size: 0.8rem; }
  .metric-label { font-size: 0.7rem; }
  .chart-container { min-height: 150px; }
}

@media (max-width: 768px) {
  .metric-value { font-size: 0.9rem; }
  .card-title { font-size: 0.75rem; }
  .metric-label { font-size: 0.65rem; }
  .chart-container { min-height: 120px; }
}

@media (max-width: 640px) {
  .metric-value { font-size: 0.8rem; }
  .card-title { font-size: 0.7rem; }
  .metric-label { font-size: 0.6rem; }
  .chart-container { min-height: 100px; }
}

/* 图表文字大小优化 */
.echarts-text-small {
  font-size: 10px !important;
}

.echarts-text-medium {
  font-size: 12px !important;
}

.echarts-text-large {
  font-size: 14px !important;
}

/* 工具提示优化 */
.echarts-tooltip {
  background: rgba(10, 22, 52, 0.95) !important;
  border: 1px solid rgba(0, 150, 255, 0.6) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

/* 图例优化 */
.echarts-legend {
  font-size: 11px !important;
  color: #FFFFFF !important;
}

/* 坐标轴标签优化 */
.echarts-axis-label {
  font-size: 10px !important;
  color: #EFEFEF !important;
}

/* 地图标签优化 */
.echarts-map-label {
  font-size: 9px !important;
  color: #FFFFFF !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* 加载状态优化 */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #7BDEFF;
  font-size: 0.9rem;
}

.chart-loading::before {
  content: "📊";
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 错误状态优化 */
.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #FF6B6B;
  font-size: 0.8rem;
  text-align: center;
  padding: 20px;
}

.chart-error::before {
  content: "⚠️";
  margin-right: 8px;
}

/* 无数据状态优化 */
.chart-no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999999;
  font-size: 0.8rem;
  text-align: center;
}

.chart-no-data::before {
  content: "📈";
  margin-right: 8px;
  opacity: 0.5;
}

/* 滚动条优化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 150, 255, 0.3);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 150, 255, 0.5);
}

/* 文字渲染优化 */
.text-crisp {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chart-container {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 打印样式优化 */
@media print {
  .chart-container {
    background: white !important;
    color: black !important;
  }
  
  .card-component {
    border: 1px solid #ccc !important;
    background: white !important;
  }
  
  .metric-value, .card-title, .metric-label {
    color: black !important;
    text-shadow: none !important;
  }
}
