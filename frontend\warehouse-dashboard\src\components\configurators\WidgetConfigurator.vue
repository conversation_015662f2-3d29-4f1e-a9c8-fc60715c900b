<template>
  <div class="flex flex-col h-full">
    <!-- 头部信息 -->
    <div class="mb-6">
      <h3 class="text-xl font-semibold text-white">组件配置</h3>
      <p class="text-sm text-gray-400">正在编辑: {{ componentType }}</p>
    </div>

    <!-- 配置表单区域 (flex-grow 会让它占据可用空间) -->
    <form class="flex-grow space-y-4">
      <!-- 通用配置项: 标题 -->
      <div>
        <label for="widget-title" class="block text-sm font-medium text-gray-300">标题</label>
        <input
          type="text"
          id="widget-title"
          v-model="editableProps.title"
          class="mt-1 block w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white focus:ring-indigo-500 focus:border-indigo-500"
        />
      </div>

      <!-- 通用配置项: 数据源 -->
      <div>
        <label for="data-source" class="block text-sm font-medium text-gray-300">数据源</label>
        <select
          id="data-source"
          v-model="editableProps.dataSourceId"
          class="mt-1 block w-full bg-gray-700 border-gray-600 rounded-md shadow-sm text-white focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option :value="null">-- 请选择数据源 --</option>
          <option v-for="source in dataSources" :key="source.id" :value="source.id">
            {{ source.name }}
          </option>
        </select>
      </div>

      <!-- 图表类型配置 - 仅在配置SalesChartCard时显示 -->
      <div v-if="componentType === 'SalesChartCard'">
        <label class="block text-sm font-medium text-gray-300 mb-2">图表类型</label>
        <div class="flex space-x-4">
          <label class="flex items-center text-white">
            <input
              type="radio"
              v-model="editableProps.chartType"
              value="line"
              class="form-radio h-4 w-4 text-indigo-600 bg-gray-700 border-gray-600 focus:ring-indigo-500"
            >
            <span class="ml-2">折线图</span>
          </label>
          <label class="flex items-center text-white">
            <input
              type="radio"
              v-model="editableProps.chartType"
              value="bar"
              class="form-radio h-4 w-4 text-indigo-600 bg-gray-700 border-gray-600 focus:ring-indigo-500"
            >
            <span class="ml-2">柱状图</span>
          </label>
        </div>
      </div>

      <!-- 未来可以根据 componentType 添加更多动态配置项 -->

    </form>

    <!-- 底部操作按钮 (flex-col 和 flex-grow 会把它推到底部) -->
    <div class="mt-6 pt-4 border-t border-gray-700 flex justify-end space-x-3">
      <button @click="emit('close')" type="button" class="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-600 rounded-md hover:bg-gray-500 focus:outline-none">
        关闭
      </button>
      <button @click="handleSave" type="button" class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none">
        应用更改
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

// 1. 定义接收的 props
// initialProps 是从 DashboardView 传来的原始数据
// componentType 仅用于显示
const props = defineProps({
  initialProps: {
    type: Object,
    required: true,
  },
  componentType: {
    type: String,
    required: true,
  }
});

// 2. 定义发出的事件
// 'save' 事件将携带新的属性数据
// 'close' 事件通知父组件关闭面板
const emit = defineEmits(['save', 'close']);

// 3. 创建 props 的本地可编辑副本
// 这是关键！我们不直接修改 props，而是修改这个副本
const editableProps = ref({ ...props.initialProps });

// 监听 initialProps 的变化，当切换组件时更新本地副本
watch(() => props.initialProps, (newProps) => {
  editableProps.value = { ...newProps };
}, { immediate: true });

// 4. 定义 "应用更改" 按钮的处理器
const handleSave = () => {
  // 发出 save 事件，并将本地修改的属性作为载荷传递出去
  emit('save', editableProps.value);
};


// --- 模拟数据 ---
// 这部分保持不变，用于填充数据源下拉列表
const dataSources = ref([
  { id: 'monthlySales', name: '月度销售数据' },
  { id: 'revenue-monthly', name: '月度营收数据' },
  { id: 'salesByCategory', name: '按类别销售数据' },
  { id: 'orderVolume', name: '订单量数据' },
  { id: 'customerData', name: '客户数据' }
]);
</script>
