<template>
  <div class="widget-configurator bg-gray-800 p-4 rounded-lg border border-blue-500 border-opacity-50">
    <h3 class="text-lg font-semibold text-blue-300 mb-4">组件配置</h3>
    
    <div class="space-y-4">
      <!-- 标题配置 -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">
          标题
        </label>
        <input
          v-model="localProps.title"
          type="text"
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="请输入组件标题"
        />
      </div>

      <!-- 数据源配置 -->
      <div v-if="localProps.dataSourceId !== undefined">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          数据源
        </label>
        <select
          v-model="localProps.dataSourceId"
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option v-for="option in chartDataSourceOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- 图表类型配置 - 仅在配置SalesChartCard时显示 -->
      <div v-if="props.componentType === 'SalesChartCard'" class="mb-4">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          图表类型
        </label>
        <div class="flex space-x-4">
          <label class="flex items-center text-white">
            <input
              type="radio"
              v-model="localProps.chartType"
              value="line"
              class="form-radio h-4 w-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500"
            >
            <span class="ml-2">折线图</span>
          </label>
          <label class="flex items-center text-white">
            <input
              type="radio"
              v-model="localProps.chartType"
              value="bar"
              class="form-radio h-4 w-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500"
            >
            <span class="ml-2">柱状图</span>
          </label>
        </div>
      </div>

      <!-- 其他通用属性配置 -->
      <div v-for="(value, key) in otherProps" :key="key">
        <label class="block text-sm font-medium text-gray-300 mb-2">
          {{ formatLabel(key) }}
        </label>
        <input
          v-if="typeof value === 'string'"
          v-model="localProps[key]"
          type="text"
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <input
          v-else-if="typeof value === 'number'"
          v-model.number="localProps[key]"
          type="number"
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <select
          v-else-if="typeof value === 'boolean'"
          v-model="localProps[key]"
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option :value="true">是</option>
          <option :value="false">否</option>
        </select>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-3 mt-6">
      <button
        @click="handleCancel"
        class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
      >
        取消
      </button>
      <button
        @click="handleSave"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        保存
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDataSourceStore } from '@/stores/dataSource'

interface Props {
  initialProps: Record<string, any>
  componentType?: string // 新增 componentType prop
}

const props = defineProps<Props>()

const emit = defineEmits<{
  save: [newProps: Record<string, any>]
  cancel: []
}>()

// 获取数据源store和图表数据源选项
const dataSourceStore = useDataSourceStore()
const chartDataSourceOptions = dataSourceStore.getChartDataSourceOptions

// 本地状态，用于编辑
const localProps = ref<Record<string, any>>({})

// 计算其他属性（除了title、dataSourceId和chartType）
const otherProps = computed(() => {
  const { title, dataSourceId, chartType, ...others } = localProps.value
  return others
})

// 格式化标签名
const formatLabel = (key: string): string => {
  const labelMap: Record<string, string> = {
    subtitle: '副标题',
    description: '描述',
    color: '颜色',
    size: '大小',
    type: '类型',
    chartType: '图表类型',
    value: '数值',
    unit: '单位',
    precision: '精度',
    trend: '趋势',
    icon: '图标'
  }
  return labelMap[key] || key
}

// 初始化本地状态
onMounted(() => {
  localProps.value = { ...props.initialProps }
})

// 处理保存
const handleSave = () => {
  emit('save', { ...localProps.value })
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.widget-configurator {
  min-width: 300px;
  max-width: 400px;
}
</style>
