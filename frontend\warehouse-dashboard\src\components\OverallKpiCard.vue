<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <div class="kpi-grid">
        <div class="kpi-item" v-for="(kpi, index) in kpiData" :key="index">
          <div class="kpi-label">{{ kpi.label }}</div>
          <div class="kpi-value" :class="kpi.trend">
            <span class="number">{{ formatNumber(kpi.value) }}</span>
            <span class="unit">{{ kpi.unit }}</span>
          </div>
          <div class="kpi-change" v-if="kpi.change">
            <i :class="getChangeIcon(kpi.change)"></i>
            <span>{{ Math.abs(kpi.change) }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: '核心运营指标'
  },
  data: {
    type: Array,
    default: () => []
  }
})

// 计算 KPI 数据
const kpiData = computed(() => {
  if (props.data.length > 0) {
    // 从实际数据计算 KPI
    const latestData = props.data[props.data.length - 1]
    return [
      {
        label: 'TOC总单量',
        value: latestData.toc_orders || 0,
        unit: '单',
        change: 5.2,
        trend: 'positive'
      },
      {
        label: 'TOB总单量', 
        value: latestData.tob_orders || 0,
        unit: '单',
        change: -2.1,
        trend: 'negative'
      },
      {
        label: '运输成本',
        value: latestData.transport_cost || 0,
        unit: '万元',
        change: 1.8,
        trend: 'positive'
      },
      {
        label: '客户投诉',
        value: latestData.customer_complaints || 0,
        unit: '件',
        change: -15.3,
        trend: 'positive'
      }
    ]
  }
  
  // 默认数据
  return [
    { label: 'TOC总单量', value: 12580, unit: '单', change: 5.2, trend: 'positive' },
    { label: 'TOB总单量', value: 8960, unit: '单', change: -2.1, trend: 'negative' },
    { label: '运输成本', value: 156.8, unit: '万元', change: 1.8, trend: 'positive' },
    { label: '客户投诉', value: 23, unit: '件', change: -15.3, trend: 'positive' }
  ]
})

// 格式化数字
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}

// 获取变化图标
const getChangeIcon = (change) => {
  return change > 0 ? 'trend-up' : 'trend-down'
}
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 20px;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  height: 100%;
}

.kpi-item {
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.kpi-item:hover {
  background: rgba(30, 144, 255, 0.2);
  transform: translateY(-2px);
}

.kpi-label {
  font-size: 0.9rem;
  color: #7BDEFF;
  margin-bottom: 8px;
}

.kpi-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.kpi-value .number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #ffffff;
}

.kpi-value .unit {
  font-size: 0.8rem;
  color: #7BDEFF;
}

.kpi-value.positive .number {
  color: #00FF7F;
}

.kpi-value.negative .number {
  color: #FF6B6B;
}

.kpi-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 0.8rem;
}

.kpi-change .trend-up {
  color: #00FF7F;
}

.kpi-change .trend-up::before {
  content: '↗';
}

.kpi-change .trend-down {
  color: #FF6B6B;
}

.kpi-change .trend-down::before {
  content: '↘';
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kpi-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .kpi-value .number {
    font-size: 1.5rem;
  }
}
</style>
