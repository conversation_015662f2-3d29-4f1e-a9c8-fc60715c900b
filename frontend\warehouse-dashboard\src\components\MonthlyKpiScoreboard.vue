<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <div class="scoreboard-grid">
        <div 
          class="kpi-score-item" 
          v-for="(kpi, index) in kpiScores" 
          :key="index"
          :class="getScoreClass(kpi.completion)"
        >
          <div class="kpi-name">{{ kpi.name }}</div>
          <div class="kpi-progress">
            <div class="progress-ring">
              <svg width="60" height="60">
                <circle
                  cx="30"
                  cy="30"
                  r="25"
                  stroke="rgba(255,255,255,0.1)"
                  stroke-width="4"
                  fill="none"
                />
                <circle
                  cx="30"
                  cy="30"
                  r="25"
                  :stroke="getProgressColor(kpi.completion)"
                  stroke-width="4"
                  fill="none"
                  stroke-linecap="round"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="getStrokeDashoffset(kpi.completion)"
                  transform="rotate(-90 30 30)"
                />
              </svg>
              <div class="progress-text">{{ kpi.completion }}%</div>
            </div>
          </div>
          <div class="kpi-details">
            <div class="target-actual">
              <span class="actual">{{ kpi.actual }}</span>
              <span class="separator">/</span>
              <span class="target">{{ kpi.target }}</span>
            </div>
            <div class="unit">{{ kpi.unit }}</div>
          </div>
        </div>
      </div>
      
      <div class="summary-stats">
        <div class="stat-item">
          <span class="stat-label">达标率</span>
          <span class="stat-value">{{ achievementRate }}%</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均完成度</span>
          <span class="stat-value">{{ averageCompletion }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '月度绩效看板'
  },
  data: {
    type: Array,
    default: () => []
  }
})

// 圆形进度条周长
const circumference = 2 * Math.PI * 25

// KPI 评分数据
const kpiScores = computed(() => {
  if (props.data.length > 0) {
    return props.data.map(item => ({
      name: item.name,
      actual: item.actual,
      target: item.target,
      unit: item.unit,
      completion: Math.round((item.actual / item.target) * 100)
    }))
  }
  
  // 默认数据
  return [
    { name: 'TOC人效', actual: 85, target: 90, unit: '单/人日', completion: 94 },
    { name: 'TOB人效', actual: 120, target: 110, unit: '单/人日', completion: 109 },
    { name: '库存准确率', actual: 96.8, target: 95, unit: '%', completion: 102 },
    { name: '及时交付率', actual: 92.5, target: 95, unit: '%', completion: 97 },
    { name: '成本控制', actual: 88, target: 85, unit: '万元', completion: 97 },
    { name: '客户满意度', actual: 4.6, target: 4.5, unit: '分', completion: 102 }
  ]
})

// 达标率计算
const achievementRate = computed(() => {
  const achieved = kpiScores.value.filter(kpi => kpi.completion >= 100).length
  return Math.round((achieved / kpiScores.value.length) * 100)
})

// 平均完成度
const averageCompletion = computed(() => {
  const total = kpiScores.value.reduce((sum, kpi) => sum + kpi.completion, 0)
  return Math.round(total / kpiScores.value.length)
})

// 获取评分等级样式
const getScoreClass = (completion) => {
  if (completion >= 100) return 'excellent'
  if (completion >= 90) return 'good'
  if (completion >= 80) return 'average'
  return 'poor'
}

// 获取进度条颜色
const getProgressColor = (completion) => {
  if (completion >= 100) return '#00FF7F'
  if (completion >= 90) return '#32CD32'
  if (completion >= 80) return '#FFD700'
  return '#FF6347'
}

// 计算进度条偏移
const getStrokeDashoffset = (completion) => {
  const progress = Math.min(completion, 100) / 100
  return circumference * (1 - progress)
}
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.scoreboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  flex: 1;
}

.kpi-score-item {
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.kpi-score-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 144, 255, 0.3);
}

.kpi-score-item.excellent {
  border-color: #00FF7F;
  background: rgba(0, 255, 127, 0.1);
}

.kpi-score-item.good {
  border-color: #32CD32;
  background: rgba(50, 205, 50, 0.1);
}

.kpi-score-item.average {
  border-color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
}

.kpi-score-item.poor {
  border-color: #FF6347;
  background: rgba(255, 99, 71, 0.1);
}

.kpi-name {
  font-size: 0.9rem;
  color: #7BDEFF;
  margin-bottom: 10px;
  font-weight: 500;
}

.kpi-progress {
  margin-bottom: 10px;
}

.progress-ring {
  position: relative;
  display: inline-block;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffffff;
}

.kpi-details {
  font-size: 0.8rem;
}

.target-actual {
  color: #ffffff;
  margin-bottom: 2px;
}

.actual {
  font-weight: bold;
}

.separator {
  color: #7BDEFF;
  margin: 0 2px;
}

.target {
  color: #7BDEFF;
}

.unit {
  color: #7BDEFF;
  font-size: 0.7rem;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(30, 144, 255, 0.3);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #7BDEFF;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scoreboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .scoreboard-grid {
    grid-template-columns: 1fr;
  }
}
</style>
