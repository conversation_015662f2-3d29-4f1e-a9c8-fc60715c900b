<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <div class="table-container">
        <table class="efficiency-table">
          <thead>
            <tr>
              <th>仓库</th>
              <th>TOC人效</th>
              <th>环比</th>
              <th>同比</th>
              <th>TOB人效</th>
              <th>环比</th>
              <th>同比</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in tableData" :key="index">
              <td class="warehouse-name">{{ row.warehouse }}</td>
              <td class="efficiency-value">{{ row.tocEfficiency }}</td>
              <td class="change-value" :class="getChangeClass(row.tocMoM)">
                <span class="change-icon">{{ getChangeIcon(row.tocMoM) }}</span>
                {{ Math.abs(row.tocMoM) }}%
              </td>
              <td class="change-value" :class="getChangeClass(row.tocYoY)">
                <span class="change-icon">{{ getChangeIcon(row.tocYoY) }}</span>
                {{ Math.abs(row.tocYoY) }}%
              </td>
              <td class="efficiency-value">{{ row.tobEfficiency }}</td>
              <td class="change-value" :class="getChangeClass(row.tobMoM)">
                <span class="change-icon">{{ getChangeIcon(row.tobMoM) }}</span>
                {{ Math.abs(row.tobMoM) }}%
              </td>
              <td class="change-value" :class="getChangeClass(row.tobYoY)">
                <span class="change-icon">{{ getChangeIcon(row.tobYoY) }}</span>
                {{ Math.abs(row.tobYoY) }}%
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="summary-row">
        <div class="summary-item">
          <span class="summary-label">平均TOC人效</span>
          <span class="summary-value">{{ averageTocEfficiency }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">平均TOB人效</span>
          <span class="summary-value">{{ averageTobEfficiency }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">最佳仓库</span>
          <span class="summary-value">{{ bestWarehouse }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '人效对比分析'
  },
  data: {
    type: Array,
    default: () => []
  }
})

// 表格数据
const tableData = computed(() => {
  if (props.data.length > 0) {
    return props.data.map(item => ({
      warehouse: item.warehouse_name,
      tocEfficiency: item.toc_efficiency,
      tocMoM: item.toc_mom_change,
      tocYoY: item.toc_yoy_change,
      tobEfficiency: item.tob_efficiency,
      tobMoM: item.tob_mom_change,
      tobYoY: item.tob_yoy_change
    }))
  }
  
  // 默认数据
  return [
    {
      warehouse: '武汉仓',
      tocEfficiency: 85.2,
      tocMoM: 5.3,
      tocYoY: 12.8,
      tobEfficiency: 120.5,
      tobMoM: -2.1,
      tobYoY: 8.9
    },
    {
      warehouse: '深圳仓',
      tocEfficiency: 92.8,
      tocMoM: 8.7,
      tocYoY: 15.2,
      tobEfficiency: 135.2,
      tobMoM: 6.4,
      tobYoY: 18.7
    },
    {
      warehouse: '黄冈仓',
      tocEfficiency: 78.9,
      tocMoM: -1.2,
      tocYoY: 6.5,
      tobEfficiency: 98.7,
      tobMoM: 3.8,
      tobYoY: 11.2
    },
    {
      warehouse: '天门仓',
      tocEfficiency: 88.6,
      tocMoM: 4.9,
      tocYoY: 9.8,
      tobEfficiency: 115.8,
      tobMoM: 1.7,
      tobYoY: 14.3
    }
  ]
})

// 平均TOC人效
const averageTocEfficiency = computed(() => {
  const total = tableData.value.reduce((sum, row) => sum + row.tocEfficiency, 0)
  return (total / tableData.value.length).toFixed(1)
})

// 平均TOB人效
const averageTobEfficiency = computed(() => {
  const total = tableData.value.reduce((sum, row) => sum + row.tobEfficiency, 0)
  return (total / tableData.value.length).toFixed(1)
})

// 最佳仓库
const bestWarehouse = computed(() => {
  if (tableData.value.length === 0) return '-'
  
  const best = tableData.value.reduce((prev, current) => {
    const prevTotal = prev.tocEfficiency + prev.tobEfficiency
    const currentTotal = current.tocEfficiency + current.tobEfficiency
    return currentTotal > prevTotal ? current : prev
  })
  
  return best.warehouse
})

// 获取变化样式类
const getChangeClass = (change) => {
  return change > 0 ? 'positive' : 'negative'
}

// 获取变化图标
const getChangeIcon = (change) => {
  return change > 0 ? '↗' : '↘'
}
</script>

<style scoped>
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

.card-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow-x: auto;
  margin-bottom: 15px;
}

.efficiency-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.efficiency-table th {
  background: rgba(30, 144, 255, 0.2);
  color: #7BDEFF;
  padding: 10px 8px;
  text-align: center;
  border: 1px solid rgba(30, 144, 255, 0.3);
  font-weight: 600;
}

.efficiency-table td {
  padding: 8px;
  text-align: center;
  border: 1px solid rgba(30, 144, 255, 0.2);
  color: #ffffff;
}

.efficiency-table tbody tr:hover {
  background: rgba(30, 144, 255, 0.1);
}

.warehouse-name {
  font-weight: 600;
  color: #7BDEFF !important;
  text-align: left !important;
}

.efficiency-value {
  font-weight: 600;
  color: #ffffff !important;
}

.change-value {
  font-size: 0.8rem;
}

.change-value.positive {
  color: #00FF7F !important;
}

.change-value.negative {
  color: #FF6B6B !important;
}

.change-icon {
  margin-right: 2px;
}

.summary-row {
  display: flex;
  justify-content: space-around;
  padding: 15px 0;
  border-top: 1px solid rgba(30, 144, 255, 0.3);
  background: rgba(30, 144, 255, 0.05);
  border-radius: 4px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 0.8rem;
  color: #7BDEFF;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 1rem;
  font-weight: bold;
  color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .efficiency-table {
    font-size: 0.8rem;
  }
  
  .efficiency-table th,
  .efficiency-table td {
    padding: 6px 4px;
  }
  
  .summary-row {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
