<template>
  <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200 transition-all duration-300 hover:shadow-lg" :class="[`kpi-${type}`, { 'kpi-loading': loading }]">
    <!-- 卡片头部 -->
    <div class="kpi-header">
      <div class="kpi-icon" v-if="icon">
        <i :class="icon"></i>
      </div>
      <div class="text-lg font-semibold text-slate-800 mb-4">{{ title }}</div>
    </div>
    
    <!-- 主要数值区域 -->
    <div class="kpi-main">
      <div class="kpi-value-container">
        <span class="kpi-value" :class="{ 'animate': animate }">
          {{ formattedValue }}
        </span>
        <span class="kpi-unit" v-if="unit">{{ unit }}</span>
      </div>
      
      <!-- 变化趋势 -->
      <div class="kpi-trend" v-if="trend !== null && trend !== undefined">
        <div class="trend-container" :class="trendClass">
          <i :class="trendIcon"></i>
          <span class="trend-value">{{ Math.abs(trend) }}%</span>
        </div>
        <div class="trend-label">{{ trendLabel }}</div>
      </div>
    </div>
    
    <!-- 底部描述或额外信息 -->
    <div class="kpi-footer" v-if="description || $slots.footer">
      <slot name="footer">
        <div class="kpi-description">{{ description }}</div>
      </slot>
    </div>
    
    <!-- 加载状态 -->
    <div class="kpi-loading-overlay" v-if="loading">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { KpiCardProps } from '../types/index'

// 定义 props
const props = withDefaults(defineProps<KpiCardProps & {
  animate?: boolean
  thousandSeparator?: boolean
}>(), {
  type: 'primary',
  unit: '',
  icon: '',
  trendLabel: '较上期',
  precision: 0,
  loading: false,
  animate: true,
  thousandSeparator: true,
  description: ''
})

// 格式化数值
const formattedValue = computed(() => {
  if (props.loading) return '--'

  let val: string | number = props.value

  // 处理数字类型
  if (typeof val === 'number') {
    let numVal: number | string = val

    // 精度处理
    if (props.precision > 0) {
      numVal = val.toFixed(props.precision)
    } else {
      numVal = Math.round(val)
    }

    // 千分位分隔符
    if (props.thousandSeparator) {
      val = numVal.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    } else {
      val = numVal
    }
  }

  return val
})

// 趋势样式类
const trendClass = computed(() => {
  if (props.trend === null || props.trend === undefined) return ''
  
  if (props.trend > 0) return 'trend-up'
  if (props.trend < 0) return 'trend-down'
  return 'trend-neutral'
})

// 趋势图标
const trendIcon = computed(() => {
  if (props.trend === null || props.trend === undefined) return ''
  
  if (props.trend > 0) return 'trend-arrow-up'
  if (props.trend < 0) return 'trend-arrow-down'
  return 'trend-arrow-right'
})
</script>

<style scoped>
/* 基础卡片样式 */
.kpi-card {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.2);
  backdrop-filter: blur(10px);
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.4);
  border-color: #00CED1;
}

/* 卡片类型样式 */
.kpi-primary {
  border-color: #1E90FF;
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.2);
}

.kpi-success {
  border-color: #00FF7F;
  box-shadow: 0 4px 20px rgba(0, 255, 127, 0.2);
}

.kpi-warning {
  border-color: #FFD700;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
}

.kpi-danger {
  border-color: #FF6B6B;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.2);
}

.kpi-info {
  border-color: #00CED1;
  box-shadow: 0 4px 20px rgba(0, 206, 209, 0.2);
}

/* 头部样式 */
.kpi-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.kpi-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7BDEFF;
  font-size: 1.2rem;
}

.kpi-title {
  font-size: 0.9rem;
  color: #7BDEFF;
  font-weight: 500;
  flex: 1;
}

/* 主要内容区域 */
.kpi-main {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kpi-value-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.kpi-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  transition: all 0.3s ease;
}

/* 响应式字体大小 */
@media (min-width: 640px) {
  .kpi-value {
    font-size: 2.2rem;
  }
}

.kpi-value.animate {
  animation: valueUpdate 0.6s ease-out;
}

.kpi-unit {
  font-size: 0.9rem;
  color: #7BDEFF;
  font-weight: normal;
}

/* 趋势样式 */
.kpi-trend {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-container {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.trend-container.trend-up {
  background: rgba(0, 255, 127, 0.1);
  color: #00FF7F;
}

.trend-container.trend-down {
  background: rgba(255, 107, 107, 0.1);
  color: #FF6B6B;
}

.trend-container.trend-neutral {
  background: rgba(123, 222, 255, 0.1);
  color: #7BDEFF;
}

.trend-label {
  font-size: 0.75rem;
  color: #7BDEFF;
  opacity: 0.8;
}

/* 趋势图标 */
.trend-arrow-up::before {
  content: '↗';
}

.trend-arrow-down::before {
  content: '↘';
}

.trend-arrow-right::before {
  content: '→';
}

/* 底部样式 */
.kpi-footer {
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px solid rgba(30, 144, 255, 0.2);
}

.kpi-description {
  font-size: 0.8rem;
  color: #7BDEFF;
  opacity: 0.8;
  line-height: 1.4;
}

/* 加载状态 */
.kpi-loading {
  pointer-events: none;
}

.kpi-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(14, 38, 92, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(30, 144, 255, 0.3);
  border-top: 2px solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes valueUpdate {
  0% {
    transform: scale(1.1);
    color: #00FF7F;
  }
  100% {
    transform: scale(1);
    color: #ffffff;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kpi-card {
    padding: 16px;
  }
  
  .kpi-value {
    font-size: 1.8rem;
  }
  
  .kpi-title {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .kpi-value {
    font-size: 1.5rem;
  }
  
  .kpi-value-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
