<template>
  <div class="component-test-page">
    <AppHeader title="组件完整性验证页面" />
    
    <div class="test-container">
      <div class="validation-summary">
        <h2>组件验证结果</h2>
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-number">{{ validationResult.totalComponents }}</div>
            <div class="stat-label">总组件数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ validationResult.completedComponents }}</div>
            <div class="stat-label">已完成</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ validationResult.missingComponents.length }}</div>
            <div class="stat-label">缺失组件</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ validationResult.validationErrors.length }}</div>
            <div class="stat-label">验证错误</div>
          </div>
        </div>
        
        <div v-if="validationResult.validationErrors.length > 0" class="error-list">
          <h3>验证错误：</h3>
          <ul>
            <li v-for="error in validationResult.validationErrors" :key="error">
              {{ error }}
            </li>
          </ul>
        </div>
      </div>

      <div class="component-grid">
        <!-- 核心指标组件 -->
        <div class="component-section">
          <h3>核心指标</h3>
          <OverallKpiCard 
            title="核心运营指标"
            :data="mockData.dailyOps"
          />
        </div>

        <!-- 趋势分析组件 -->
        <div class="component-section">
          <h3>趋势分析</h3>
          <DailyOpsTrendChart 
            title="运营趋势分析"
            chart-id="trend-chart-test"
            :data="mockData.dailyOps"
          />
        </div>

        <!-- 状态监控组件 -->
        <div class="component-section">
          <h3>状态监控</h3>
          <WarehouseStatusGauge 
            title="仓储状态监控"
            chart-id="gauge-chart-test"
            :data="mockData.warehouseStatus"
          />
        </div>

        <!-- 库存管理组件 -->
        <div class="component-section">
          <h3>库存管理</h3>
          <InventoryHealthPanel 
            title="库存健康度"
            :data="mockData.inventoryHealth"
          />
        </div>

        <!-- 人力成本组件 -->
        <div class="component-section">
          <h3>人力成本</h3>
          <LaborCostPieChart 
            title="人力成本结构"
            chart-id="labor-chart-test"
            :data="mockData.laborCost"
          />
        </div>

        <!-- 绩效管理组件 -->
        <div class="component-section">
          <h3>绩效管理</h3>
          <MonthlyKpiScoreboard 
            title="月度绩效看板"
            :data="mockData.monthlyKpi"
          />
        </div>

        <!-- 人效分析组件 -->
        <div class="component-section">
          <h3>人效分析</h3>
          <PersonnelEfficiencyTable 
            title="人效对比分析"
            :data="mockData.personnelEfficiency"
          />
        </div>

        <!-- 服务质量组件 -->
        <div class="component-section">
          <h3>服务质量</h3>
          <ServiceQualityTracker 
            title="服务质量跟踪"
            chart-id="service-chart-test"
            :data="mockData.serviceQuality"
          />
        </div>

        <!-- 原有饼图组件 -->
        <div class="component-section">
          <h3>通用图表</h3>
          <PieChart 
            title="商品分类占比"
            chart-id="pie-chart-test"
            :data="mockData.categoryData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  validateComponentRegistry, 
  getComponentStats,
  AppHeader,
  PieChart,
  OverallKpiCard,
  DailyOpsTrendChart,
  WarehouseStatusGauge,
  InventoryHealthPanel,
  LaborCostPieChart,
  MonthlyKpiScoreboard,
  PersonnelEfficiencyTable,
  ServiceQualityTracker
} from '../components/ComponentRegistry.js'

// 验证结果
const validationResult = ref({})
const componentStats = ref({})

// 模拟数据
const mockData = ref({
  dailyOps: [
    { date: '2024-01-15', toc_orders: 12580, tob_orders: 8960, transport_cost: 156.8, customer_complaints: 23 }
  ],
  warehouseStatus: {
    utilization_rate: 78.5,
    remaining_capacity: '2.1万m³'
  },
  inventoryHealth: {
    turnover_rate: 2.3,
    accuracy_rate: 96.8
  },
  laborCost: {
    regular_hours: 6800,
    temp_hours: 3200
  },
  monthlyKpi: [
    { name: 'TOC人效', actual: 85, target: 90, unit: '单/人日' },
    { name: 'TOB人效', actual: 120, target: 110, unit: '单/人日' },
    { name: '库存准确率', actual: 96.8, target: 95, unit: '%' }
  ],
  personnelEfficiency: [
    { warehouse_name: '武汉仓', toc_efficiency: 85.2, toc_mom_change: 5.3, toc_yoy_change: 12.8 }
  ],
  serviceQuality: [
    { date: '2024-01-15', customer_complaints: 23, reverse_logistics_orders: 156 }
  ],
  categoryData: [
    { value: 35, name: '电子产品' },
    { value: 25, name: '服装鞋帽' },
    { value: 20, name: '食品饮料' }
  ]
})

onMounted(() => {
  // 执行组件验证
  validationResult.value = validateComponentRegistry()
  componentStats.value = getComponentStats()
  
  console.log('组件验证结果:', validationResult.value)
  console.log('组件统计信息:', componentStats.value)
})
</script>

<style scoped>
.component-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
}

.validation-summary {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.validation-summary h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #00FF7F;
}

.stat-label {
  font-size: 0.9rem;
  color: #7BDEFF;
  margin-top: 5px;
}

.error-list {
  background: rgba(255, 99, 71, 0.1);
  border: 1px solid #FF6347;
  border-radius: 4px;
  padding: 15px;
}

.error-list h3 {
  color: #FF6347;
  margin-bottom: 10px;
}

.error-list ul {
  margin: 0;
  padding-left: 20px;
}

.error-list li {
  color: #ffffff;
  margin-bottom: 5px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.component-section {
  background: rgba(14, 38, 92, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  min-height: 400px;
}

.component-section h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .component-grid {
    grid-template-columns: 1fr;
  }
}
</style>
