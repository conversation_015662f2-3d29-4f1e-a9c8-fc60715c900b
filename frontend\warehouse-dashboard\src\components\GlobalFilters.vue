<!-- src/components/GlobalFilters.vue -->
<template>
  <div class="global-filters-container">
    <div class="filters-header">
      <h3 class="filters-title">🔍 全局筛选</h3>
      <div class="filters-summary">{{ filterStore.filterSummary }}</div>
    </div>
    
    <div class="filters-content">
      <!-- 产品类别筛选器 -->
      <div class="filter-group">
        <label for="category-filter" class="filter-label">产品类别:</label>
        <select 
          id="category-filter"
          :value="categoryFilter"
          @change="updateCategory"
          class="filter-select"
        >
          <option value="">全部</option>
          <option value="电子产品">电子产品</option>
          <option value="图书音像">图书音像</option>
          <option value="家居用品">家居用品</option>
        </select>
      </div>

      <!-- 省份筛选器显示（只读，由地图控制） -->
      <div class="filter-group">
        <label class="filter-label">省份筛选:</label>
        <div class="filter-display">
          <span v-if="provinceFilter" class="filter-tag">
            {{ provinceFilter }}
            <button @click="clearProvinceFilter" class="filter-clear-btn">×</button>
          </span>
          <span v-else class="filter-placeholder">点击地图选择省份</span>
        </div>
      </div>

      <!-- 清除所有筛选按钮 -->
      <div class="filter-actions">
        <button 
          @click="clearAllFilters" 
          :disabled="!filterStore.hasFilter"
          class="clear-all-btn"
        >
          清除所有筛选
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useFilterStore } from '@/stores/filterStore';

const filterStore = useFilterStore();

// 计算属性
const categoryFilter = computed(() => filterStore.filters.category || '');
const provinceFilter = computed(() => filterStore.filters.province || '');

// 方法
const updateCategory = (event) => {
  const value = event.target.value;
  console.log(`[GlobalFilters] 更新产品类别筛选: ${value}`);
  filterStore.setFilter({ key: 'category', value: value || null });
};

const clearProvinceFilter = () => {
  console.log('[GlobalFilters] 清除省份筛选');
  filterStore.clearFilter('province');
};

const clearAllFilters = () => {
  console.log('[GlobalFilters] 清除所有筛选');
  filterStore.clearFilter();
};
</script>

<style scoped>
.global-filters-container {
  background: rgba(16, 33, 62, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.filters-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.5);
}

.filters-summary {
  font-size: 0.9rem;
  color: #A0D8EF;
  font-style: italic;
}

.filters-content {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.filter-label {
  font-size: 0.9rem;
  color: #B3E5FC;
  white-space: nowrap;
  font-weight: 500;
}

.filter-select {
  background: rgba(10, 22, 52, 0.8);
  border: 1px solid rgba(0, 150, 255, 0.4);
  border-radius: 6px;
  padding: 6px 12px;
  color: #FFFFFF;
  font-size: 0.9rem;
  min-width: 120px;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(0, 150, 255, 0.8);
  box-shadow: 0 0 8px rgba(0, 150, 255, 0.3);
}

.filter-select:hover {
  border-color: rgba(0, 150, 255, 0.6);
}

.filter-display {
  min-height: 32px;
  display: flex;
  align-items: center;
}

.filter-tag {
  background: rgba(0, 150, 255, 0.2);
  border: 1px solid rgba(0, 150, 255, 0.4);
  border-radius: 16px;
  padding: 4px 12px;
  color: #7BDEFF;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-clear-btn {
  background: none;
  border: none;
  color: #FF6B6B;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.filter-clear-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  color: #FF8A8A;
}

.filter-placeholder {
  color: #666;
  font-style: italic;
  font-size: 0.85rem;
}

.filter-actions {
  margin-left: auto;
}

.clear-all-btn {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.4);
  border-radius: 6px;
  padding: 6px 16px;
  color: #FF8A8A;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-all-btn:hover:not(:disabled) {
  background: rgba(255, 107, 107, 0.3);
  border-color: rgba(255, 107, 107, 0.6);
  color: #FFAAAA;
}

.clear-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
    width: 100%;
  }
  
  .filter-actions {
    margin-left: 0;
    width: 100%;
  }
  
  .clear-all-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .global-filters-container {
    padding: 12px;
  }
  
  .filters-title {
    font-size: 1rem;
  }
  
  .filters-summary {
    font-size: 0.8rem;
  }
  
  .filter-label {
    font-size: 0.8rem;
  }
  
  .filter-select {
    font-size: 0.8rem;
    padding: 5px 10px;
  }
}
</style>
