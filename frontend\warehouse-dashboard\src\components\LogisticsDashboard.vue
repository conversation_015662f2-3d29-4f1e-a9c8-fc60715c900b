<template>
  <div class="logistics-dashboard p-4 sm:p-6 lg:p-8" ref="dashboardRef">
    <!-- 应用头部 -->
    <AppHeader title="物流大数据仪表盘" />

    <!-- 加载状态 -->
    <LoadingIndicator v-if="isLoading" />

    <!-- 错误状态 -->
    <ErrorMessage v-else-if="error" :message="error" />

    <!-- 仪表盘内容 -->
    <div v-else class="dashboard-content">
      <!-- 筛选器区域 -->
      <section class="filters-section mb-6">
        <DateRangeFilter @update:dateRange="handleDateRangeChange" />

        <!-- 区域筛选指示器 -->
        <div class="region-indicator" v-if="selectedRegion !== '总计'">
          <div class="region-badge">
            <span class="region-label">当前区域:</span>
            <span class="region-name">{{ selectedRegion }}</span>
          </div>
          <button class="reset-btn" @click="dashboardStore.selectRegion('总计')">
            查看全国
          </button>
        </div>
      </section>

      <!-- 主网格布局 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6 p-6">
        <!-- KPI 卡片 -->
        <template v-if="filteredKeyMetrics">
          <div v-for="(kpi, index) in filteredKeyMetrics" :key="`kpi-${index}`" class="col-span-1">
            <KpiCard
              :title="kpi.title"
              :value="kpi.value"
              :unit="kpi.unit"
              :type="kpi.type"
              :icon="kpi.icon"
              :trend="kpi.trend"
              :trend-label="kpi.trendLabel"
              :description="kpi.description"
              :loading="false"
              :precision="kpi.precision"
            />
          </div>
        </template>

        <!-- 月度订单趋势图表 -->
        <div v-if="filteredMonthlyOrders" class="col-span-1 md:col-span-2 lg:col-span-3">
          <ChartCard :title="`月度订单趋势 - ${selectedRegion}`">
            <MonthlyOrdersChart
              :chartData="filteredMonthlyOrders"
              :title="`月度订单趋势 - ${selectedRegion}`"
              subtitle="过去六个月订单量变化"
              :loading="false"
              :showLabel="true"
              :enableAnimation="true"
              themeColor="#00CED1"
              chartHeight="100%"
            />
          </ChartCard>
        </div>

        <!-- 区域分布饼图 -->
        <div v-if="availableRegions && availableRegions.length > 0" class="col-span-1 md:col-span-1 lg:col-span-2">
          <ChartCard title="区域分布">
            <LaborCostPieChart
              :chartData="availableRegions.map(region => ({ name: region, value: Math.random() * 30 + 10 }))"
              title="区域分布"
              subtitle="点击区域查看详细数据"
              :loading="false"
              :showPercentage="true"
              :enableAnimation="true"
              chartHeight="100%"
            />
          </ChartCard>
        </div>

        <!-- 物流网络地图 -->
        <div v-if="filteredLogisticsData" class="col-span-1 md:col-span-2 lg:col-span-3">
          <ChartCard :title="`物流网络 - ${selectedRegion}`">
            <LogisticsMap
              :warehouseData="filteredLogisticsData.warehouseData"
              :routeData="filteredLogisticsData.routeData"
              :title="`物流网络 - ${selectedRegion}`"
              subtitle="仓库分布与运输路线"
              :loading="false"
              :enableAnimation="true"
              mapHeight="100%"
            />
          </ChartCard>
        </div>

        <!-- 实时订单流水 -->
        <div v-if="realTimeOrdersData" class="col-span-1 md:col-span-1 lg:col-span-2">
          <ChartCard title="实时订单流水">
            <RealTimeOrderList
              :listData="realTimeOrdersData"
              title="实时订单流水"
              subtitle="全国物流订单实时监控"
              :scrollDuration="25"
              :maxRows="8"
              :showControls="false"
              :loading="false"
            />
          </ChartCard>
        </div>

        <!-- 人效分析 -->
        <div class="col-span-1 md:col-span-1 lg:col-span-2">
          <ChartCard title="人效分析">
            <div class="card-content" id="efficiency-analysis">
              <!-- 人效分析数据 -->
            </div>
          </ChartCard>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useDashboardStore } from '../stores/dashboard'
import type { DateRange } from '../types/index'
// 导入已存在的组件
import KpiCard from './KpiCard.vue'
import ChartCard from './ChartCard.vue'
import LaborCostPieChart from './LaborCostPieChart.vue'
import DateRangeFilter from './DateRangeFilter.vue'
import LoadingIndicator from './LoadingIndicator.vue'
import ErrorMessage from './ErrorMessage.vue'

// 临时组件占位符（用于演示加载和错误状态）
const AppHeader = {
  template: '<div class="app-header"><h1>{{ title }}</h1></div>',
  props: ['title']
}
const MonthlyOrdersChart = {
  template: '<div class="chart-placeholder">月度订单图表</div>',
  props: ['chartData', 'title', 'subtitle', 'loading', 'showLabel', 'enableAnimation', 'themeColor', 'chartHeight']
}
const LogisticsMap = {
  template: '<div class="map-placeholder">物流地图</div>',
  props: ['warehouseData', 'routeData', 'title', 'subtitle', 'loading', 'enableAnimation', 'mapHeight']
}
const RealTimeOrderList = {
  template: '<div class="list-placeholder">实时订单列表</div>',
  props: ['listData', 'title', 'subtitle', 'scrollDuration', 'maxRows', 'showControls', 'loading']
}

// 响应式数据
const dashboardRef = ref<HTMLElement | null>(null)

// 实例化Dashboard Store
const dashboardStore = useDashboardStore()

// 使用storeToRefs从Store中解构出所有需要在模板中使用的数据，保持响应性
const {
  isLoading,
  error,
  realTimeOrdersData,
  selectedRegion,
  // 筛选后的数据
  filteredKeyMetrics,
  filteredMonthlyOrders,
  filteredLogisticsData,
  availableRegions
} = storeToRefs(dashboardStore)

// Store实例已经通过 useDashboardStore() 获取

// 处理日期范围变化
const handleDateRangeChange = (dateRange: DateRange) => {
  console.log('📅 日期范围变化:', dateRange)
  dashboardStore.setDateRange(dateRange)
}

// 设置响应式字体大小（移动端优先，固定断点）
const setResponsiveFontSize = (): void => {
  const clientWidth = window.innerWidth
  if (!clientWidth) return

  // 使用固定断点而不是无限制的vw单位
  if (clientWidth < 640) {
    // 手机端
    document.documentElement.style.fontSize = '14px'
  } else if (clientWidth < 768) {
    // 大手机端
    document.documentElement.style.fontSize = '15px'
  } else if (clientWidth < 1024) {
    // 平板端
    document.documentElement.style.fontSize = '16px'
  } else if (clientWidth < 1280) {
    // 小桌面端
    document.documentElement.style.fontSize = '17px'
  } else {
    // 大桌面端
    document.documentElement.style.fontSize = '18px'
  }
}

// Store实例已经通过 useDashboardStore() 获取，直接使用 dashboardStore.method() 调用

// 定时器引用
let timeInterval: number | null = null
let dataInterval: number | null = null

onMounted(async () => {
  // 设置初始字体大小
  setResponsiveFontSize()

  // 监听窗口大小变化
  window.addEventListener('resize', setResponsiveFontSize)

  // 初始化时间
  dashboardStore.updateCurrentTime()

  // 启动时间更新定时器
  timeInterval = setInterval(() => {
    dashboardStore.updateCurrentTime()
  }, 1000)

  // 加载初始数据
  await dashboardStore.loadDashboardData()

  // 启动数据刷新定时器（每5分钟刷新一次）
  dataInterval = setInterval(() => {
    dashboardStore.refreshData()
  }, 5 * 60 * 1000)

  // 订阅实时数据更新
  dashboardStore.subscribeToRealTime()

  console.log('✅ LogisticsDashboard 初始化完成')
})

onUnmounted(() => {
  // 清理定时器和事件监听器
  window.removeEventListener('resize', setResponsiveFontSize)
  if (timeInterval) clearInterval(timeInterval)
  if (dataInterval) clearInterval(dataInterval)

  // 取消实时数据订阅
  dashboardStore.unsubscribeFromRealTime()

  console.log('🧹 LogisticsDashboard 已清理')
})
</script>

<style scoped>
/* 全局字体大小设置 - 基于视口宽度的动态缩放 */
.logistics-dashboard {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', 'Arial', sans-serif;
  overflow: hidden;
  position: relative;
}

/* 旧的加载和错误状态样式已移除，现在使用独立组件 */

/* 仪表盘内容 */
.dashboard-content {
  width: 100%;
  height: 100%;

  /* CSS Grid 五行布局 */
  display: grid;
  grid-template-rows:
    auto                     /* 筛选器区域 */
    minmax(8rem, 12rem)     /* KPI 指标行 */
    1fr                      /* 主要图表区域 */
    minmax(15rem, 20rem)     /* 数据详情区域 */
    minmax(3rem, 4rem);      /* 底部状态栏 */
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 1rem;
  box-sizing: border-box;
}

/* 筛选器区域 */
.filters-section {
  grid-row: 1;
  display: flex;
  justify-content: center;
  padding: 0 1rem;
}

/* KPI 指标区域 */
.kpi-section {
  grid-row: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.kpi-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  width: 100%;
  max-width: 80rem;
}

/* KPI容器响应式布局 */
@media (min-width: 768px) {
  .kpi-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .kpi-container {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 区域筛选指示器 */
.region-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.region-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.region-label {
  color: #7BDEFF;
  font-size: 0.9rem;
}

.region-name {
  color: #FFD700;
  font-weight: 600;
  font-size: 1rem;
}

.reset-btn {
  background: rgba(0, 255, 127, 0.8);
  color: #ffffff;
  border: 1px solid #00FF7F;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: rgba(0, 255, 127, 1);
  transform: translateY(-1px);
}

/* 主要图表区域 */
.main-charts-section {
  grid-row: 3;
  display: flex;
  align-items: stretch;
  justify-content: center;
}

.chart-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  width: 100%;
  max-width: 80rem;
}

/* 图表容器响应式布局 */
@media (min-width: 768px) {
  .chart-container {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .chart-container {
    grid-template-columns: 2fr 1fr;
  }
}

.chart-card {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 0.75rem;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 0.25rem 1.25rem rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.5rem 1.875rem rgba(30, 144, 255, 0.4);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.5);
  color: #7BDEFF;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover,
.control-btn.active {
  background: rgba(30, 144, 255, 0.8);
  color: #ffffff;
}

.chart-content {
  height: calc(100% - 4rem);
  min-height: 12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7BDEFF;
  font-size: 0.875rem;
}

/* 数据详情区域 */
.details-section {
  grid-row: 4;
  display: flex;
  align-items: stretch;
  justify-content: center;
}

.details-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  width: 100%;
  max-width: 80rem;
}

/* 详情容器响应式布局 */
@media (min-width: 768px) {
  .details-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .details-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

.detail-card {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 0.75rem;
  padding: 1.25rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 0.25rem 1.25rem rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.5rem 1.875rem rgba(30, 144, 255, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0;
}

.status-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: #00FF7F;
  box-shadow: 0 0 0.375rem rgba(0, 255, 127, 0.5);
}

.status-indicator.active {
  animation: pulse 2s infinite;
}

.card-content {
  height: calc(100% - 3rem);
  min-height: 8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7BDEFF;
  font-size: 0.875rem;
}

/* 地图卡片特殊样式 */
.detail-card.map-card {
  grid-column: span 3; /* 占满整行 */
  min-height: 20rem;
}

.detail-card.map-card .card-content {
  padding: 0;
}

/* 底部状态栏 */
.status-section {
  grid-row: 5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  width: 100%;
  max-width: 80rem;
  padding: 0.75rem 1.5rem;
  background: rgba(14, 38, 92, 0.4);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.status-label {
  font-size: 0.75rem;
  color: #7BDEFF;
  opacity: 0.8;
}

.status-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
}

.status-value.online {
  color: #00FF7F;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .logistics-dashboard {
    grid-template-rows: 
      minmax(7rem, 10rem)
      1fr
      minmax(12rem, 16rem)
      minmax(2.5rem, 3.5rem);
  }
  
  .kpi-container {
    gap: 1rem;
  }
  
  .chart-container,
  .details-container {
    gap: 1rem;
  }
}

@media (max-width: 1200px) {
  .logistics-dashboard {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .details-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .status-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .logistics-dashboard {
    grid-template-rows: auto auto auto auto;
    height: auto;
    min-height: 100vh;
  }
  
  .kpi-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-container {
    grid-template-columns: 1fr;
  }
  
  .details-container {
    grid-template-columns: 1fr;
  }
  
  .status-container {
    grid-template-columns: 1fr;
  }
}

/* 动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 临时占位符组件样式 */
.app-header {
  background: rgba(14, 38, 92, 0.8);
  color: #7BDEFF;
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.chart-placeholder,
.map-placeholder,
.list-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 8px;
  color: #7BDEFF;
  font-size: 1.1rem;
  font-weight: 500;
}
</style>
