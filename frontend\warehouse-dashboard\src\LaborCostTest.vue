<template>
  <div class="labor-cost-test">
    <AppHeader title="人力成本饼图组件测试" />
    
    <div class="test-container">
      <div class="controls-section">
        <h2>控制面板</h2>
        <div class="controls-grid">
          <div class="control-group">
            <label>数据集选择:</label>
            <select v-model="selectedDataset" @change="switchDataset">
              <option value="labor">人力成本构成</option>
              <option value="warehouse">仓库成本分布</option>
              <option value="transport">运输成本分析</option>
              <option value="empty">空数据测试</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="showPercentage" />
              显示百分比
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="enableAnimation" />
              启用动画
            </label>
          </div>
          
          <div class="control-group">
            <label>
              <input type="checkbox" v-model="isLoading" />
              加载状态
            </label>
          </div>
          
          <div class="control-group">
            <button @click="refreshData" class="refresh-btn">
              刷新数据
            </button>
          </div>
        </div>
      </div>

      <div class="charts-section">
        <h2>图表展示</h2>
        <div class="charts-grid">
          <!-- 标准尺寸图表 -->
          <div class="chart-wrapper">
            <h3>标准尺寸 (400x300)</h3>
            <LaborCostPieChart
              :chartData="currentData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :showPercentage="showPercentage"
              :enableAnimation="enableAnimation"
              chartHeight="300px"
            />
          </div>
          
          <!-- 大尺寸图表 -->
          <div class="chart-wrapper">
            <h3>大尺寸 (500x400)</h3>
            <LaborCostPieChart
              :chartData="currentData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :showPercentage="showPercentage"
              :enableAnimation="enableAnimation"
              chartHeight="400px"
            />
          </div>
          
          <!-- 小尺寸图表 -->
          <div class="chart-wrapper small">
            <h3>小尺寸 (300x200)</h3>
            <LaborCostPieChart
              :chartData="currentData"
              :title="currentTitle"
              :subtitle="currentSubtitle"
              :loading="isLoading"
              :showPercentage="showPercentage"
              :enableAnimation="enableAnimation"
              chartHeight="200px"
            />
          </div>
          
          <!-- 自定义数据图表 -->
          <div class="chart-wrapper">
            <h3>自定义数据</h3>
            <LaborCostPieChart
              :chartData="customData"
              title="自定义成本分析"
              subtitle="实时数据更新"
              :loading="false"
              :showPercentage="true"
              :enableAnimation="true"
              chartHeight="300px"
            />
          </div>
        </div>
      </div>

      <div class="data-section">
        <h2>当前数据</h2>
        <div class="data-display">
          <pre>{{ JSON.stringify(currentData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import AppHeader from './components/AppHeader.vue'
import LaborCostPieChart from './components/LaborCostPieChart.vue'

// 响应式数据
const selectedDataset = ref('labor')
const showPercentage = ref(true)
const enableAnimation = ref(true)
const isLoading = ref(false)

// 预设数据集
const datasets = {
  labor: {
    title: '人力成本构成',
    subtitle: '按工种分类统计',
    data: [
      { name: '正式工成本', value: 55000 },
      { name: '劳务工成本', value: 32000 },
      { name: '临时工成本', value: 18000 },
      { name: '加班费用', value: 12000 },
      { name: '福利支出', value: 8000 }
    ]
  },
  warehouse: {
    title: '仓库成本分布',
    subtitle: '各仓库运营成本对比',
    data: [
      { name: '武汉仓', value: 45000 },
      { name: '深圳仓', value: 52000 },
      { name: '黄冈仓', value: 28000 },
      { name: '天门仓', value: 35000 }
    ]
  },
  transport: {
    title: '运输成本分析',
    subtitle: '按运输方式分类',
    data: [
      { name: '公路运输', value: 68000 },
      { name: '铁路运输', value: 42000 },
      { name: '航空运输', value: 25000 },
      { name: '水路运输', value: 15000 },
      { name: '快递配送', value: 38000 },
      { name: '同城配送', value: 22000 }
    ]
  },
  empty: {
    title: '空数据测试',
    subtitle: '测试无数据状态',
    data: []
  }
}

// 自定义数据（模拟实时更新）
const customData = ref([
  { name: '设备维护', value: 15000 },
  { name: '能源消耗', value: 28000 },
  { name: '租金费用', value: 35000 }
])

// 当前数据
const currentData = computed(() => datasets[selectedDataset.value].data)
const currentTitle = computed(() => datasets[selectedDataset.value].title)
const currentSubtitle = computed(() => datasets[selectedDataset.value].subtitle)

// 切换数据集
const switchDataset = () => {
  console.log('切换到数据集:', selectedDataset.value)
}

// 刷新数据
const refreshData = () => {
  // 模拟数据刷新
  const currentDataset = datasets[selectedDataset.value]
  if (currentDataset.data.length > 0) {
    currentDataset.data.forEach(item => {
      // 随机变化 ±20%
      const change = (Math.random() - 0.5) * 0.4
      item.value = Math.round(item.value * (1 + change))
    })
  }
  
  // 更新自定义数据
  customData.value.forEach(item => {
    const change = (Math.random() - 0.5) * 0.3
    item.value = Math.round(item.value * (1 + change))
  })
  
  console.log('数据已刷新')
}

// 模拟实时数据更新
setInterval(() => {
  if (Math.random() > 0.7) {
    refreshData()
  }
}, 5000)
</script>

<style scoped>
.labor-cost-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
}

.test-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.controls-section,
.charts-section,
.data-section {
  margin-bottom: 40px;
}

.controls-section h2,
.charts-section h2,
.data-section h2 {
  color: #7BDEFF;
  margin-bottom: 20px;
  font-size: 1.5rem;
  border-bottom: 2px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 10px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  background: rgba(14, 38, 92, 0.6);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #1E90FF;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  color: #7BDEFF;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group select {
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid #1E90FF;
  color: #ffffff;
  padding: 8px;
  border-radius: 4px;
}

.control-group input[type="checkbox"] {
  accent-color: #1E90FF;
}

.refresh-btn {
  background: rgba(0, 255, 127, 0.8);
  color: #ffffff;
  border: 1px solid #00FF7F;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(0, 255, 127, 1);
  transform: translateY(-2px);
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.chart-wrapper {
  background: rgba(14, 38, 92, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  min-height: 400px;
}

.chart-wrapper.small {
  min-height: 300px;
}

.chart-wrapper h3 {
  color: #7BDEFF;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1rem;
}

.data-display {
  background: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  padding: 20px;
  overflow-x: auto;
}

.data-display pre {
  color: #7BDEFF;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  margin: 0;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .controls-grid {
    grid-template-columns: 1fr;
  }
}
</style>
