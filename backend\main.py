from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List
import pandas as pd
import os

# 创建 DailyMetrics Pydantic 模型
class DailyMetrics(BaseModel):
    date: str
    warehouse_name: str
    shipping_orders: int
    pending_orders: int
    efficiency_orders_per_hour: float

# 创建 FastAPI 应用实例
app = FastAPI()

# 配置 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# 读取Excel文件的函数
def read_excel_data():
    """读取data.xlsx文件并返回数据"""
    excel_file = "data.xlsx"

    # 检查文件是否存在
    if not os.path.exists(excel_file):
        raise HTTPException(status_code=404, detail=f"Excel文件 {excel_file} 不存在")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)

        # 检查必需的列是否存在
        required_columns = ['date', 'warehouse_name', 'shipping_orders', 'pending_orders', 'efficiency_orders_per_hour']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件缺少必需的列: {missing_columns}"
            )

        # 转换数据类型并处理空值
        df['date'] = df['date'].astype(str)
        df['warehouse_name'] = df['warehouse_name'].astype(str)
        df['shipping_orders'] = pd.to_numeric(df['shipping_orders'], errors='coerce').fillna(0).astype(int)
        df['pending_orders'] = pd.to_numeric(df['pending_orders'], errors='coerce').fillna(0).astype(int)
        df['efficiency_orders_per_hour'] = pd.to_numeric(df['efficiency_orders_per_hour'], errors='coerce').fillna(0.0)

        return df

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取Excel文件时出错: {str(e)}")

# GET API 接口
@app.get("/api/v1/metrics/{warehouse}", response_model=List[DailyMetrics])
async def get_metrics(warehouse: str):
    """获取指定仓库的指标数据"""
    try:
        # 读取Excel数据
        df = read_excel_data()

        # 如果指定了具体仓库，则过滤数据；否则返回所有数据
        if warehouse.lower() != "all":
            df = df[df['warehouse_name'].str.contains(warehouse, case=False, na=False)]

        # 转换为DailyMetrics对象列表
        metrics_data = []
        for _, row in df.iterrows():
            metrics_data.append(DailyMetrics(
                date=row['date'],
                warehouse_name=row['warehouse_name'],
                shipping_orders=int(row['shipping_orders']),
                pending_orders=int(row['pending_orders']),
                efficiency_orders_per_hour=float(row['efficiency_orders_per_hour'])
            ))

        return metrics_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理数据时出错: {str(e)}")
