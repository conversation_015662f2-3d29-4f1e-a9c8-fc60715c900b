<template>
  <div class="logistics-viz-container">
    <!-- 顶部统计信息 -->
    <div class="stats-header">
      <div class="stat-item">
        <div class="stat-icon">
          <i class="fas fa-map-marker-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalNodes }}</div>
          <div class="stat-label">物流节点</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">
          <i class="fas fa-truck"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalOrders }}</div>
          <div class="stat-label">今日订单</div>
        </div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">+{{ growthRate }}%</div>
          <div class="stat-label">增长率</div>
        </div>
      </div>
    </div>

    <!-- 主要可视化区域 -->
    <div class="main-viz-area">
      <!-- 左侧节点列表 -->
      <div class="nodes-panel">
        <div class="panel-title">
          <i class="fas fa-warehouse"></i>
          <span>仓库节点</span>
        </div>
        <div class="nodes-list">
          <div 
            v-for="(node, index) in logisticsNodes" 
            :key="index"
            class="node-item"
            :class="{ 'node-active': selectedNode === index }"
            @click="selectNode(index)"
          >
            <div class="node-indicator" :style="{ backgroundColor: node.color }"></div>
            <div class="node-info">
              <div class="node-name">{{ node.name }}</div>
              <div class="node-stats">
                <span class="node-orders">{{ node.orders }}单</span>
                <span class="node-efficiency" :class="getEfficiencyClass(node.efficiency)">
                  {{ node.efficiency }}%
                </span>
              </div>
            </div>
            <div class="node-trend">
              <i :class="node.trend > 0 ? 'fas fa-arrow-up trend-up' : 'fas fa-arrow-down trend-down'"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央可视化图表 -->
      <div class="central-chart">
        <div class="chart-container" ref="chartContainer"></div>
        <div class="chart-overlay">
          <div class="overlay-stats">
            <div class="overlay-item">
              <span class="overlay-label">实时处理</span>
              <span class="overlay-value">{{ realtimeProcessing }}</span>
            </div>
            <div class="overlay-item">
              <span class="overlay-label">平均效率</span>
              <span class="overlay-value">{{ averageEfficiency }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧数据流 -->
      <div class="data-stream-panel">
        <div class="panel-title">
          <i class="fas fa-stream"></i>
          <span>实时数据流</span>
        </div>
        <div class="stream-content" ref="streamContent">
          <div 
            v-for="(item, index) in dataStream" 
            :key="item.id"
            class="stream-item"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <div class="stream-time">{{ item.time }}</div>
            <div class="stream-event" :style="{ color: item.color }">
              <i :class="item.icon"></i>
              <span>{{ item.event }}</span>
            </div>
            <div class="stream-value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

interface LogisticsNode {
  name: string;
  orders: number;
  efficiency: number;
  color: string;
  trend: number;
}

interface StreamItem {
  id: string;
  time: string;
  event: string;
  value: string;
  color: string;
  icon: string;
}

interface Props {
  nodes?: LogisticsNode[];
  height?: string;
}

const props = withDefaults(defineProps<Props>(), {
  nodes: () => [],
  height: '100%'
});

// 响应式数据
const selectedNode = ref(0);
const chartContainer = ref<HTMLElement>();
const streamContent = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 默认物流节点数据
const defaultNodes: LogisticsNode[] = [
  { name: '武汉仓库', orders: 2340, efficiency: 98.5, color: '#00D4FF', trend: 12.5 },
  { name: '黄冈仓库', orders: 1890, efficiency: 96.2, color: '#4ECDC4', trend: 8.3 },
  { name: '天门仓库', orders: 1680, efficiency: 94.8, color: '#00FF88', trend: -2.1 },
  { name: '深圳仓库', orders: 1560, efficiency: 99.1, color: '#FFEAA7', trend: 15.7 },
  { name: '北京仓库', orders: 1200, efficiency: 97.3, color: '#DDA0DD', trend: 6.9 },
  { name: '上海仓库', orders: 980, efficiency: 95.7, color: '#FF6B6B', trend: 4.2 }
];

// 数据流
const dataStream = ref<StreamItem[]>([]);

// 计算属性
const logisticsNodes = computed(() => props.nodes.length > 0 ? props.nodes : defaultNodes);
const totalNodes = computed(() => logisticsNodes.value.length);
const totalOrders = computed(() => logisticsNodes.value.reduce((sum, node) => sum + node.orders, 0));
const growthRate = computed(() => {
  const avgTrend = logisticsNodes.value.reduce((sum, node) => sum + node.trend, 0) / logisticsNodes.value.length;
  return avgTrend.toFixed(1);
});
const realtimeProcessing = computed(() => Math.floor(Math.random() * 50) + 20);
const averageEfficiency = computed(() => {
  const avg = logisticsNodes.value.reduce((sum, node) => sum + node.efficiency, 0) / logisticsNodes.value.length;
  return avg.toFixed(1);
});

// 方法
const selectNode = (index: number) => {
  selectedNode.value = index;
  updateChart();
};

const getEfficiencyClass = (efficiency: number) => {
  if (efficiency >= 98) return 'efficiency-excellent';
  if (efficiency >= 95) return 'efficiency-good';
  if (efficiency >= 90) return 'efficiency-normal';
  return 'efficiency-poor';
};

// 生成数据流项目
const generateStreamItem = (): StreamItem => {
  const events = [
    { text: '订单创建', icon: 'fas fa-plus-circle', color: '#00D4FF' },
    { text: '支付完成', icon: 'fas fa-credit-card', color: '#00FF88' },
    { text: '发货通知', icon: 'fas fa-shipping-fast', color: '#4ECDC4' },
    { text: '签收确认', icon: 'fas fa-check-circle', color: '#FFEAA7' },
    { text: '异常处理', icon: 'fas fa-exclamation-triangle', color: '#FF6B6B' }
  ];
  
  const event = events[Math.floor(Math.random() * events.length)];
  const now = new Date();
  
  return {
    id: Math.random().toString(36).substr(2, 9),
    time: `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`,
    event: event.text,
    value: `¥${(Math.random() * 1000 + 100).toFixed(0)}`,
    color: event.color,
    icon: event.icon
  };
};

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;
  
  chartInstance = echarts.init(chartContainer.value);
  updateChart();
  
  // 响应式处理
  const resizeHandler = () => chartInstance?.resize();
  window.addEventListener('resize', resizeHandler);
  
  return () => window.removeEventListener('resize', resizeHandler);
};

// 更新图表
const updateChart = () => {
  if (!chartInstance) return;
  
  const selectedNodeData = logisticsNodes.value[selectedNode.value];
  
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: `${selectedNodeData.name} - 实时监控`,
      left: 'center',
      top: '5%',
      textStyle: {
        color: '#7BDEFF',
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(6, 22, 74, 0.9)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: { color: '#ffffff' }
    },
    series: [
      // 环形进度图
      {
        name: '效率指标',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '55%'],
        startAngle: 90,
        data: [
          {
            value: selectedNodeData.efficiency,
            name: '当前效率',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: selectedNodeData.color },
                  { offset: 1, color: selectedNodeData.color + '80' }
                ]
              }
            }
          },
          {
            value: 100 - selectedNodeData.efficiency,
            name: '剩余',
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            label: { show: false },
            labelLine: { show: false }
          }
        ],
        label: {
          show: true,
          position: 'center',
          formatter: `{a|${selectedNodeData.efficiency}%}\n{b|效率}`,
          rich: {
            a: {
              fontSize: 20,
              fontWeight: 'bold',
              color: selectedNodeData.color
            },
            b: {
              fontSize: 12,
              color: '#7BDEFF',
              padding: [5, 0, 0, 0]
            }
          }
        }
      }
    ]
  };
  
  chartInstance.setOption(option);
};

// 添加数据流项目
const addStreamItem = () => {
  dataStream.value.unshift(generateStreamItem());
  if (dataStream.value.length > 15) {
    dataStream.value.pop();
  }
};

// 定时器
let streamInterval: number | null = null;

// 生命周期
onMounted(() => {
  initChart();
  
  // 初始化数据流
  for (let i = 0; i < 8; i++) {
    dataStream.value.push(generateStreamItem());
  }
  
  // 开始数据流更新
  streamInterval = setInterval(addStreamItem, 3000);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  if (streamInterval) {
    clearInterval(streamInterval);
  }
});
</script>

<style scoped>
.logistics-viz-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  color: #ffffff;
}

/* 顶部统计 */
.stats-header {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-item {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.8) 0%, rgba(14, 38, 92, 0.6) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(5px);
}

.stat-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00D4FF;
  font-size: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: bold;
  color: #00D4FF;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-label {
  font-size: 0.8rem;
  color: #A0D8EF;
  margin-top: 2px;
}

/* 主要可视化区域 */
.main-viz-area {
  flex: 1;
  display: grid;
  grid-template-columns: 250px 1fr 200px;
  gap: 15px;
}

/* 节点面板 */
.nodes-panel {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 15px;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  font-size: 0.9rem;
  color: #7BDEFF;
  font-weight: 600;
}

.nodes-list {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.node-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.node-item:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
}

.node-active {
  background: rgba(0, 255, 136, 0.1);
  border-color: rgba(0, 255, 136, 0.4);
}

.node-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow: 0 0 8px currentColor;
}

.node-info {
  flex: 1;
}

.node-name {
  font-size: 0.9rem;
  color: #7BDEFF;
  font-weight: 500;
  margin-bottom: 3px;
}

.node-stats {
  display: flex;
  gap: 8px;
  font-size: 0.7rem;
}

.node-orders {
  color: #A0D8EF;
}

.efficiency-excellent { color: #00FF88; }
.efficiency-good { color: #4ECDC4; }
.efficiency-normal { color: #FFEAA7; }
.efficiency-poor { color: #FF6B6B; }

.node-trend {
  font-size: 0.8rem;
}

.trend-up { color: #00FF88; }
.trend-down { color: #FF6B6B; }

/* 中央图表 */
.central-chart {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  position: relative;
  backdrop-filter: blur(8px);
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.chart-overlay {
  position: absolute;
  bottom: 15px;
  left: 15px;
  right: 15px;
  background: rgba(6, 22, 74, 0.8);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 6px;
  padding: 10px;
  backdrop-filter: blur(5px);
}

.overlay-stats {
  display: flex;
  justify-content: space-around;
}

.overlay-item {
  text-align: center;
}

.overlay-label {
  display: block;
  font-size: 0.7rem;
  color: #A0D8EF;
  margin-bottom: 3px;
}

.overlay-value {
  font-size: 1rem;
  font-weight: bold;
  color: #00D4FF;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
}

/* 数据流面板 */
.data-stream-panel {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.stream-content {
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.stream-item {
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 6px;
  background: rgba(0, 0, 0, 0.2);
  animation: slideInRight 0.5s ease-out;
}

.stream-time {
  font-size: 0.7rem;
  color: #7BDEFF;
  font-family: monospace;
  margin-bottom: 2px;
}

.stream-event {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
  margin-bottom: 2px;
}

.stream-value {
  font-size: 0.7rem;
  color: #A0D8EF;
  text-align: right;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-viz-area {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .stats-header {
    grid-template-columns: 1fr;
  }
}
</style>
