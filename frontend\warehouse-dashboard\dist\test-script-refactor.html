<!DOCTYPE html>
<html>
<head>
    <title>WidgetConfigurator Script 逻辑重构测试</title>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "<PERSON><PERSON><PERSON><PERSON>", Aria<PERSON>, sans-serif;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%); 
            color: white; 
            line-height: 1.6;
            min-height: 100vh;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(16, 33, 62, 0.9);
            border-radius: 12px;
            border: 2px solid #40e0d0;
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
        }
        .header h1 {
            color: #7BDEFF;
            font-size: 2.2rem;
            margin: 0;
            text-shadow: 0 0 25px rgba(123, 222, 255, 0.7);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #40e0d0; 
            border-radius: 10px; 
            background: rgba(16, 33, 62, 0.8);
            backdrop-filter: blur(12px);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 18px;
            margin: 18px 0;
        }
        .test-card {
            background: linear-gradient(135deg, rgba(10, 22, 52, 0.7) 0%, rgba(16, 33, 62, 0.5) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 18px;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.25);
            transform: translateY(-1px);
        }
        .test-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #7BDEFF;
            margin-bottom: 12px;
            text-shadow: 0 0 8px rgba(123, 222, 255, 0.4);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #00FF88; box-shadow: 0 0 10px rgba(0, 255, 136, 0.5); }
        .status-warning { background: #FFD700; box-shadow: 0 0 10px rgba(255, 215, 0, 0.5); }
        .status-error { background: #FF6B6B; box-shadow: 0 0 10px rgba(255, 107, 107, 0.5); }
        .status-info { background: #00BFFF; box-shadow: 0 0 10px rgba(0, 191, 255, 0.5); }
        
        .test-item {
            margin: 8px 0;
            padding: 10px 12px;
            background: rgba(0, 0, 0, 0.25);
            border-radius: 5px;
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }
        .test-item:hover {
            background: rgba(0, 0, 0, 0.35);
        }
        .test-item.success { border-left-color: #00FF88; }
        .test-item.warning { border-left-color: #FFD700; }
        .test-item.error { border-left-color: #FF6B6B; }
        .test-item.info { border-left-color: #00BFFF; }
        
        .quick-link {
            display: inline-block;
            padding: 10px 16px;
            background: rgba(0, 150, 255, 0.2);
            border: 1px solid rgba(0, 150, 255, 0.4);
            border-radius: 6px;
            color: #7BDEFF;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            margin: 6px;
        }
        .quick-link:hover {
            background: rgba(0, 150, 255, 0.3);
            border-color: rgba(0, 150, 255, 0.6);
            color: #FFFFFF;
            transform: translateY(-1px);
        }
        
        .instructions {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .instructions h3 {
            color: #FFD700;
            margin-top: 0;
        }
        
        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: rgba(0, 150, 255, 0.3);
            border: 1px solid rgba(0, 150, 255, 0.6);
            border-radius: 50%;
            font-weight: bold;
            margin-right: 8px;
            color: #7BDEFF;
            font-size: 0.85rem;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 1.8rem; }
            .test-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 WidgetConfigurator Script 逻辑重构测试</h1>
        <div style="margin-top: 15px; color: #B3E5FC; font-size: 0.9rem;">
            测试时间: <span id="test-time"></span> | 服务器: http://localhost:5173/
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 测试页面导航</h2>
        <a href="http://localhost:5173/" class="quick-link" target="_blank">🏠 主应用</a>
        <a href="http://localhost:5173/test-widget-configurator-refactor.html" class="quick-link" target="_blank">🎛️ 组件重构测试</a>
        <a href="http://localhost:5173/test-configurator-refactor.html" class="quick-link" target="_blank">🔧 配置器逻辑重构测试</a>
    </div>

    <div class="instructions">
        <h3>🧪 Script 逻辑重构测试指南</h3>
        <p><strong>重构目标</strong>: 简化WidgetConfigurator的script逻辑，使其更专注于核心职责：接收初始属性，创建可编辑副本，发送保存事件。</p>
        
        <h4>📋 核心简化点</h4>
        <div style="margin: 15px 0;">
            <div style="margin: 8px 0;">
                <span class="step-number">1</span>
                <strong>移除TypeScript</strong>: 从TypeScript改为纯JavaScript，简化类型定义
            </div>
            <div style="margin: 8px 0;">
                <span class="step-number">2</span>
                <strong>简化Props定义</strong>: 使用对象语法而非接口定义
            </div>
            <div style="margin: 8px 0;">
                <span class="step-number">3</span>
                <strong>专注核心功能</strong>: 只保留必要的状态管理和事件处理
            </div>
            <div style="margin: 8px 0;">
                <span class="step-number">4</span>
                <strong>保持响应式</strong>: 确保props变化时本地副本正确更新
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 重构前后对比</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">❌ 重构前 (复杂版本)</div>
                <div class="code-block">
&lt;script setup lang="ts"&gt;
import { ref, computed, onMounted, watch } from 'vue'
import { useDataSourceStore } from '@/stores/dataSource'

interface Props {
  initialProps: Record&lt;string, any&gt;
  componentType?: string
}

const props = defineProps&lt;Props&gt;()

const emit = defineEmits&lt;{
  save: [newProps: Record&lt;string, any&gt;]
  close: []
}&gt;()

// 获取数据源store
const dataSourceStore = useDataSourceStore()

// 复杂的计算属性
const dataSources = computed(() =&gt; [
  // ... 数据源列表
])

// 可编辑的属性
const editableProps = ref&lt;Record&lt;string, any&gt;&gt;({})

// 复杂的监听逻辑
watch(() =&gt; props.initialProps, (newProps) =&gt; {
  editableProps.value = { ...newProps }
}, { immediate: true })

// 处理保存
const handleSave = () =&gt; {
  emit('save', { ...editableProps.value })
}
&lt;/script&gt;
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">✅ 重构后 (简化版本)</div>
                <div class="code-block">
&lt;script setup&gt;
import { ref, watch } from 'vue';

// 1. 定义接收的 props
const props = defineProps({
  initialProps: {
    type: Object,
    required: true,
  },
  componentType: {
    type: String,
    required: true,
  }
});

// 2. 定义发出的事件
const emit = defineEmits(['save', 'close']);

// 3. 创建 props 的本地可编辑副本
const editableProps = ref({ ...props.initialProps });

// 监听 initialProps 的变化
watch(() =&gt; props.initialProps, (newProps) =&gt; {
  editableProps.value = { ...newProps };
}, { immediate: true });

// 4. 定义 "应用更改" 按钮的处理器
const handleSave = () =&gt; {
  emit('save', editableProps.value);
};

// --- 模拟数据 ---
const dataSources = ref([
  { id: 'monthlySales', name: '月度销售数据' },
  // ... 其他数据源
]);
&lt;/script&gt;
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 功能测试清单</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">📝 Props 定义</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    initialProps 对象类型，必需属性
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    componentType 字符串类型，必需属性
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    使用对象语法而非TypeScript接口
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    Props验证正确工作
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">📡 事件定义</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    save 事件携带编辑后的属性
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    close 事件通知父组件关闭
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    使用数组语法而非TypeScript类型
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    事件正确触发和传递数据
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">🔄 状态管理</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    editableProps 创建props的本地副本
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    watch 监听 initialProps 变化
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    immediate: true 确保初始化
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    组件切换时数据正确更新
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">💾 保存处理</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    handleSave 方法简洁明了
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    直接传递 editableProps.value
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    无需额外的数据处理
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    事件载荷格式正确
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">📊 数据源管理</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    dataSources 使用简单的 ref
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    静态数据列表，无复杂计算
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    数据格式清晰 (id, name)
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    下拉框正确显示选项
                </div>
            </div>

            <div class="test-card">
                <div class="test-title">🔧 技术实现</div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    移除TypeScript，使用纯JavaScript
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    最小化导入，只需要 ref 和 watch
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    代码行数显著减少
                </div>
                <div class="test-item info">
                    <span class="status-indicator status-info"></span>
                    逻辑清晰，易于理解
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎉 重构优势总结</h2>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>代码简化</strong>: 从复杂的TypeScript接口改为简单的对象定义
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>职责专注</strong>: 专注于核心功能，移除不必要的复杂性
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>易于维护</strong>: 代码行数减少，逻辑更清晰
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>性能优化</strong>: 减少不必要的计算和依赖
        </div>
        <div class="test-item success">
            <span class="status-indicator status-success"></span>
            <strong>功能完整</strong>: 保持所有必要功能，无功能缺失
        </div>
    </div>

    <script>
        // 设置测试时间
        document.getElementById('test-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 页面加载完成提示
        window.onload = function() {
            console.log('📝 WidgetConfigurator Script 逻辑重构测试页面加载完成');
            console.log('📋 请按照测试清单验证重构后的脚本逻辑');
            console.log('🎯 重点测试: Props定义、事件处理、状态管理、保存功能');
            console.log('✨ 新特性: 简化语法、专注职责、易于维护');
        };
    </script>
</body>
</html>
