<!DOCTYPE html>
<html>
<head>
    <title>大屏展示独立测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
            color: white;
            overflow: hidden;
        }
        
        .big-screen-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部标题栏 */
        .screen-header {
            height: 80px;
            background: rgba(16, 33, 62, 0.9);
            border-bottom: 2px solid #40e0d0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            backdrop-filter: blur(10px);
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            font-size: 2.5rem;
            filter: drop-shadow(0 0 10px rgba(64, 224, 208, 0.6));
        }
        
        .main-title {
            font-size: 2rem;
            font-weight: 600;
            color: #7BDEFF;
            margin: 0;
            text-shadow: 0 0 20px rgba(123, 222, 255, 0.6);
        }
        
        .time-display {
            font-size: 1.5rem;
            font-weight: 500;
            color: #00FF88;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
        }
        
        .status-indicators {
            display: flex;
            gap: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00FF88;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* 主要内容区域 */
        .screen-content {
            flex: 1;
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            gap: 20px;
            padding: 20px;
            overflow: hidden;
        }
        
        /* 侧边面板 */
        .left-panels, .right-panels {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .data-panel {
            background: rgba(16, 33, 62, 0.8);
            border: 1px solid rgba(64, 224, 208, 0.3);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            overflow: hidden;
            flex: 1;
        }
        
        .panel-header {
            background: rgba(64, 224, 208, 0.1);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(64, 224, 208, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-header h3 {
            margin: 0;
            font-size: 1.1rem;
            color: #7BDEFF;
        }
        
        .panel-icon {
            font-size: 1.2rem;
        }
        
        .panel-content {
            padding: 20px;
        }
        
        /* 中央地图区域 */
        .center-map-area {
            display: flex;
            flex-direction: column;
            background: rgba(16, 33, 62, 0.8);
            border: 2px solid rgba(64, 224, 208, 0.4);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .map-header {
            background: rgba(64, 224, 208, 0.1);
            padding: 20px;
            border-bottom: 1px solid rgba(64, 224, 208, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .map-header h2 {
            margin: 0;
            font-size: 1.5rem;
            color: #7BDEFF;
            text-shadow: 0 0 15px rgba(123, 222, 255, 0.6);
        }
        
        .map-controls {
            display: flex;
            gap: 10px;
        }
        
        .map-btn {
            padding: 8px 16px;
            background: rgba(64, 224, 208, 0.2);
            border: 1px solid rgba(64, 224, 208, 0.4);
            border-radius: 6px;
            color: #7BDEFF;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .map-btn:hover, .map-btn.active {
            background: rgba(64, 224, 208, 0.3);
            border-color: rgba(64, 224, 208, 0.6);
        }
        
        .map-content {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: radial-gradient(circle at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
        }
        
        .map-placeholder {
            width: 80%;
            height: 80%;
            background: rgba(0, 0, 0, 0.3);
            border: 2px dashed rgba(64, 224, 208, 0.5);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .map-placeholder h3 {
            color: #7BDEFF;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        
        .map-placeholder p {
            color: #B3E5FC;
            font-size: 1.1rem;
            margin: 10px 0;
        }
        
        /* 底部状态栏 */
        .screen-footer {
            height: 50px;
            background: rgba(16, 33, 62, 0.9);
            border-top: 1px solid rgba(64, 224, 208, 0.3);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            font-size: 0.9rem;
        }
        
        .data-source-status {
            display: flex;
            gap: 20px;
        }
        
        .source-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .source-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00FF88;
        }
        
        .switch-view-btn {
            padding: 8px 16px;
            background: rgba(64, 224, 208, 0.2);
            border: 1px solid rgba(64, 224, 208, 0.4);
            border-radius: 6px;
            color: #7BDEFF;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .switch-view-btn:hover {
            background: rgba(64, 224, 208, 0.3);
            border-color: rgba(64, 224, 208, 0.6);
        }
        
        /* 数据项样式 */
        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(64, 224, 208, 0.2);
            margin: 10px 0;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #7BDEFF;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #B3E5FC;
        }
        
        .order-item {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            border-left: 3px solid #00FF88;
        }
        
        .order-id {
            color: #7BDEFF;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .order-route {
            color: #B3E5FC;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="big-screen-container">
        <!-- 顶部标题栏 -->
        <div class="screen-header">
            <div class="logo-section">
                <div class="logo-icon">🚛</div>
                <h1 class="main-title">物流大数据监控中心</h1>
            </div>
            <div class="time-display" id="current-time"></div>
            <div class="status-indicators">
                <div class="status-item">
                    <span class="status-dot"></span>
                    <span>系统正常</span>
                </div>
                <div class="status-item">
                    <span class="status-dot"></span>
                    <span>实时监控</span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="screen-content">
            <!-- 左侧面板 -->
            <div class="left-panels">
                <div class="data-panel">
                    <div class="panel-header">
                        <h3>今日概览</h3>
                        <div class="panel-icon">📊</div>
                    </div>
                    <div class="panel-content">
                        <div class="stat-item">
                            <div class="stat-value">12,847</div>
                            <div class="stat-label">总订单数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥2,847万</div>
                            <div class="stat-label">总营收</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">1,247</div>
                            <div class="stat-label">在途车辆</div>
                        </div>
                    </div>
                </div>

                <div class="data-panel">
                    <div class="panel-header">
                        <h3>仓库状态</h3>
                        <div class="panel-icon">🏭</div>
                    </div>
                    <div class="panel-content">
                        <div class="stat-item">
                            <div class="stat-value">98.5%</div>
                            <div class="stat-label">武汉仓</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">96.2%</div>
                            <div class="stat-label">黄冈仓</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">94.8%</div>
                            <div class="stat-label">天门仓</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中央地图区域 -->
            <div class="center-map-area">
                <div class="map-header">
                    <h2>全国物流分布图</h2>
                    <div class="map-controls">
                        <button class="map-btn active">热力图</button>
                        <button class="map-btn">流向图</button>
                        <button class="map-btn">路径图</button>
                    </div>
                </div>
                <div class="map-content">
                    <div class="map-placeholder">
                        <h3>🗺️ 中央地图区域</h3>
                        <p>地图中央化布局成功实现</p>
                        <p>占据核心视觉位置</p>
                        <p>支持热力图、流向图、路径图切换</p>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panels">
                <div class="data-panel">
                    <div class="panel-header">
                        <h3>实时订单</h3>
                        <div class="panel-icon">📦</div>
                    </div>
                    <div class="panel-content">
                        <div class="order-item">
                            <div class="order-id">WL2024001</div>
                            <div class="order-route">武汉 → 深圳</div>
                        </div>
                        <div class="order-item">
                            <div class="order-id">WL2024002</div>
                            <div class="order-route">黄冈 → 天门</div>
                        </div>
                        <div class="order-item">
                            <div class="order-id">WL2024003</div>
                            <div class="order-route">深圳 → 武汉</div>
                        </div>
                    </div>
                </div>

                <div class="data-panel">
                    <div class="panel-header">
                        <h3>性能指标</h3>
                        <div class="panel-icon">⚡</div>
                    </div>
                    <div class="panel-content">
                        <div class="stat-item">
                            <div class="stat-value">2.3小时</div>
                            <div class="stat-label">平均配送时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">95.8%</div>
                            <div class="stat-label">准时率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">4.7/5.0</div>
                            <div class="stat-label">客户满意度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部状态栏 -->
        <div class="screen-footer">
            <div>数据更新时间: <span id="update-time"></span></div>
            <div class="data-source-status">
                <span class="source-item">
                    <span class="source-dot"></span>
                    订单系统
                </span>
                <span class="source-item">
                    <span class="source-dot"></span>
                    仓储系统
                </span>
                <span class="source-item">
                    <span class="source-dot"></span>
                    运输系统
                </span>
            </div>
            <button class="switch-view-btn" onclick="alert('大屏展示功能正常工作！')">
                功能测试
            </button>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeStr;
            document.getElementById('update-time').textContent = now.toLocaleTimeString('zh-CN');
        }

        // 初始化
        updateTime();
        setInterval(updateTime, 1000);

        // 地图控制按钮切换
        document.querySelectorAll('.map-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.map-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        console.log('🖥️ 大屏展示独立测试页面加载完成');
        console.log('✅ 地图中央化布局正常显示');
        console.log('✅ 三栏布局结构正确');
        console.log('✅ 实时时间更新正常');
        console.log('✅ 视觉效果符合预期');
    </script>
</body>
</html>
