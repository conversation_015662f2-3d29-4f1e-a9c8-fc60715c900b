<template>
  <div class="big-screen-container">
    <!-- 顶部标题栏 -->
    <div class="screen-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">🚛</div>
          <h1 class="main-title">物流大数据监控中心</h1>
        </div>
      </div>
      <div class="header-center">
        <div class="time-display">{{ currentTime }}</div>
      </div>
      <div class="header-right">
        <div class="status-indicators">
          <div class="status-item">
            <span class="status-dot online"></span>
            <span>系统正常</span>
          </div>
          <div class="status-item">
            <span class="status-dot"></span>
            <span>实时监控</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="screen-content">
      <!-- 左侧面板 -->
      <div class="left-panels">
        <div class="panel-group">
          <!-- 总览数据面板 -->
          <div class="data-panel overview-panel">
            <div class="panel-header">
              <h3>今日概览</h3>
              <div class="panel-icon">📊</div>
            </div>
            <div class="panel-content">
              <div class="overview-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ todayStats.totalOrders }}</div>
                  <div class="stat-label">总订单数</div>
                  <div class="stat-trend up">↗ +12.5%</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ todayStats.totalRevenue }}</div>
                  <div class="stat-label">总营收</div>
                  <div class="stat-trend up">↗ +8.3%</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ todayStats.activeVehicles }}</div>
                  <div class="stat-label">在途车辆</div>
                  <div class="stat-trend">→ 0%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 仓库状态面板 -->
          <div class="data-panel warehouse-panel">
            <div class="panel-header">
              <h3>仓库状态</h3>
              <div class="panel-icon">🏭</div>
            </div>
            <div class="panel-content">
              <div class="warehouse-list">
                <div v-for="warehouse in warehouseStatus" :key="warehouse.id" class="warehouse-item">
                  <div class="warehouse-name">{{ warehouse.name }}</div>
                  <div class="warehouse-capacity">
                    <div class="capacity-bar">
                      <div class="capacity-fill" :style="{ width: warehouse.utilization + '%' }"></div>
                    </div>
                    <span class="capacity-text">{{ warehouse.utilization }}%</span>
                  </div>
                  <div class="warehouse-status" :class="warehouse.status">{{ warehouse.statusText }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央地图区域 -->
      <div class="center-map-area">
        <div class="map-container">
          <div class="map-header">
            <h2>全国物流分布图</h2>
            <div class="map-controls">
              <button class="map-btn active" @click="switchMapMode('heat')">热力图</button>
              <button class="map-btn" @click="switchMapMode('flow')">流向图</button>
              <button class="map-btn" @click="switchMapMode('route')">路径图</button>
            </div>
          </div>
          <div class="map-content">
            <!-- 地图占位符 -->
            <div class="map-placeholder">
              <h3>🗺️ 中央地图区域</h3>
              <p>地图中央化布局成功实现</p>
              <p>{{ mapData.warehouses.length }} 个仓库节点</p>
              <p>{{ mapData.routes.length }} 条运输路线</p>
              <p>支持热力图、流向图、路径图切换</p>
            </div>
          </div>
          <div class="map-legend">
            <div class="legend-item">
              <span class="legend-color high"></span>
              <span>高密度</span>
            </div>
            <div class="legend-item">
              <span class="legend-color medium"></span>
              <span>中密度</span>
            </div>
            <div class="legend-item">
              <span class="legend-color low"></span>
              <span>低密度</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panels">
        <div class="panel-group">
          <!-- 实时订单面板 -->
          <div class="data-panel orders-panel">
            <div class="panel-header">
              <h3>实时订单</h3>
              <div class="panel-icon">📦</div>
            </div>
            <div class="panel-content">
              <div class="orders-list">
                <div v-for="order in realtimeOrders" :key="order.id" class="order-item">
                  <div class="order-info">
                    <div class="order-id">{{ order.id }}</div>
                    <div class="order-route">{{ order.from }} → {{ order.to }}</div>
                  </div>
                  <div class="order-status" :class="order.status">
                    {{ order.statusText }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 性能指标面板 -->
          <div class="data-panel performance-panel">
            <div class="panel-header">
              <h3>性能指标</h3>
              <div class="panel-icon">⚡</div>
            </div>
            <div class="panel-content">
              <div class="performance-metrics">
                <div class="metric-item">
                  <div class="metric-name">平均配送时间</div>
                  <div class="metric-value">2.3小时</div>
                  <div class="metric-gauge">
                    <div class="gauge-fill" style="width: 76%"></div>
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-name">准时率</div>
                  <div class="metric-value">95.8%</div>
                  <div class="metric-gauge">
                    <div class="gauge-fill" style="width: 96%"></div>
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-name">客户满意度</div>
                  <div class="metric-value">4.7/5.0</div>
                  <div class="metric-gauge">
                    <div class="gauge-fill" style="width: 94%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="screen-footer">
      <div class="footer-left">
        <span>数据更新时间: {{ lastUpdateTime }}</span>
      </div>
      <div class="footer-center">
        <div class="data-source-status">
          <span class="source-item">
            <span class="source-dot active"></span>
            订单系统
          </span>
          <span class="source-item">
            <span class="source-dot active"></span>
            仓储系统
          </span>
          <span class="source-item">
            <span class="source-dot active"></span>
            运输系统
          </span>
        </div>
      </div>
      <div class="footer-right">
        <button class="switch-view-btn" @click="switchToNormalView">
          切换到配置模式
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 定义事件
const emit = defineEmits(['switchToNormalView'])

// 响应式数据
const currentTime = ref('')
const lastUpdateTime = ref('')
const mapMode = ref('heat')

// 模拟数据
const todayStats = ref({
  totalOrders: '12,847',
  totalRevenue: '¥2,847万',
  activeVehicles: '1,247'
})

const warehouseStatus = ref([
  { id: 1, name: '武汉仓', utilization: 78, status: 'normal', statusText: '正常' },
  { id: 2, name: '黄冈仓', utilization: 65, status: 'normal', statusText: '正常' },
  { id: 3, name: '天门仓', utilization: 92, status: 'warning', statusText: '接近满载' },
  { id: 4, name: '深圳仓', utilization: 45, status: 'normal', statusText: '正常' }
])

const realtimeOrders = ref([
  { id: 'WL2024001', from: '武汉', to: '深圳', status: 'shipping', statusText: '运输中' },
  { id: 'WL2024002', from: '黄冈', to: '天门', status: 'delivered', statusText: '已送达' },
  { id: 'WL2024003', from: '深圳', to: '武汉', status: 'processing', statusText: '处理中' },
  { id: 'WL2024004', from: '天门', to: '黄冈', status: 'shipping', statusText: '运输中' }
])

const mapData = ref({
  warehouses: [
    { name: '北京', value: [116.40, 39.90, 150] },
    { name: '上海', value: [121.47, 31.23, 200] },
    { name: '广州', value: [113.23, 23.16, 180] },
    { name: '深圳', value: [114.07, 22.62, 220] },
    { name: '武汉', value: [114.31, 30.52, 120] },
    { name: '成都', value: [104.06, 30.67, 160] },
    { name: '西安', value: [108.95, 34.27, 100] },
    { name: '杭州', value: [120.19, 30.26, 140] }
  ],
  routes: [
    { coords: [[116.40, 39.90], [121.47, 31.23]] }, // 北京-上海
    { coords: [[121.47, 31.23], [113.23, 23.16]] }, // 上海-广州
    { coords: [[113.23, 23.16], [114.07, 22.62]] }, // 广州-深圳
    { coords: [[114.31, 30.52], [104.06, 30.67]] }, // 武汉-成都
    { coords: [[104.06, 30.67], [108.95, 34.27]] }, // 成都-西安
    { coords: [[108.95, 34.27], [114.31, 30.52]] }  // 西安-武汉
  ]
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  lastUpdateTime.value = now.toLocaleTimeString('zh-CN')
}

const switchMapMode = (mode) => {
  mapMode.value = mode
  // 更新地图显示模式
}

const switchToNormalView = () => {
  emit('switchToNormalView')
}

// 生命周期
let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.big-screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
  color: white;
  font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 顶部标题栏 */
.screen-header {
  height: 80px;
  background: rgba(16, 33, 62, 0.9);
  border-bottom: 2px solid #40e0d0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  backdrop-filter: blur(10px);
}

.header-left .logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(64, 224, 208, 0.6));
}

.main-title {
  font-size: 2rem;
  font-weight: 600;
  color: #7BDEFF;
  margin: 0;
  text-shadow: 0 0 20px rgba(123, 222, 255, 0.6);
}

.header-center .time-display {
  font-size: 1.5rem;
  font-weight: 500;
  color: #00FF88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
}

.header-right .status-indicators {
  display: flex;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #FF6B6B;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background: #00FF88;
}

/* 主要内容区域 */
.screen-content {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

/* 侧边面板 */
.left-panels, .right-panels {
  display: flex;
  flex-direction: column;
}

.panel-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.data-panel {
  background: rgba(16, 33, 62, 0.8);
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  flex: 1;
}

.panel-header {
  background: rgba(64, 224, 208, 0.1);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(64, 224, 208, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #7BDEFF;
}

.panel-icon {
  font-size: 1.2rem;
}

.panel-content {
  padding: 20px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

/* 概览面板样式 */
.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(64, 224, 208, 0.2);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #7BDEFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #B3E5FC;
  margin-bottom: 5px;
}

.stat-trend {
  font-size: 0.8rem;
  font-weight: 500;
}

.stat-trend.up {
  color: #00FF88;
}

/* 中央地图区域 */
.center-map-area {
  display: flex;
  flex-direction: column;
  background: rgba(16, 33, 62, 0.8);
  border: 2px solid rgba(64, 224, 208, 0.4);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.map-header {
  background: rgba(64, 224, 208, 0.1);
  padding: 20px;
  border-bottom: 1px solid rgba(64, 224, 208, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #7BDEFF;
  text-shadow: 0 0 15px rgba(123, 222, 255, 0.6);
}

.map-controls {
  display: flex;
  gap: 10px;
}

.map-btn {
  padding: 8px 16px;
  background: rgba(64, 224, 208, 0.2);
  border: 1px solid rgba(64, 224, 208, 0.4);
  border-radius: 6px;
  color: #7BDEFF;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-btn:hover, .map-btn.active {
  background: rgba(64, 224, 208, 0.3);
  border-color: rgba(64, 224, 208, 0.6);
}

.map-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
}

.map-placeholder {
  width: 80%;
  height: 80%;
  background: rgba(0, 0, 0, 0.3);
  border: 2px dashed rgba(64, 224, 208, 0.5);
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.map-placeholder h3 {
  color: #7BDEFF;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-shadow: 0 0 15px rgba(123, 222, 255, 0.6);
}

.map-placeholder p {
  color: #B3E5FC;
  font-size: 1.1rem;
  margin: 8px 0;
}

/* 地图占位符样式已在上面定义 */

.map-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(64, 224, 208, 0.3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 5px 0;
  font-size: 0.9rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.legend-color.high { background: #FF6B6B; }
.legend-color.medium { background: #FFD700; }
.legend-color.low { background: #00FF88; }

/* 底部状态栏 */
.screen-footer {
  height: 50px;
  background: rgba(16, 33, 62, 0.9);
  border-top: 1px solid rgba(64, 224, 208, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  font-size: 0.9rem;
}

.data-source-status {
  display: flex;
  gap: 20px;
}

.source-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.source-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #FF6B6B;
}

.source-dot.active {
  background: #00FF88;
}

.switch-view-btn {
  padding: 8px 16px;
  background: rgba(64, 224, 208, 0.2);
  border: 1px solid rgba(64, 224, 208, 0.4);
  border-radius: 6px;
  color: #7BDEFF;
  cursor: pointer;
  transition: all 0.3s ease;
}

.switch-view-btn:hover {
  background: rgba(64, 224, 208, 0.3);
  border-color: rgba(64, 224, 208, 0.6);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .screen-content {
    grid-template-columns: 250px 1fr 250px;
  }
}

@media (max-width: 1200px) {
  .main-title {
    font-size: 1.5rem;
  }
  
  .screen-content {
    grid-template-columns: 200px 1fr 200px;
  }
}
</style>
