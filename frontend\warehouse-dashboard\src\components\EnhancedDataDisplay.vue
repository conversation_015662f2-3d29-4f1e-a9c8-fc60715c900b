<template>
  <div class="data-display-container">
    <!-- 数据指标网格 -->
    <div class="metrics-grid">
      <div 
        v-for="(metric, index) in displayMetrics" 
        :key="index"
        class="metric-item"
        :class="{ 'metric-highlight': metric.highlight }"
      >
        <div class="metric-icon">
          <div class="icon-glow" :style="{ backgroundColor: metric.color }"></div>
          <i :class="metric.icon"></i>
        </div>
        <div class="metric-content">
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-value" :style="{ color: metric.color }">
            {{ formatValue(metric.value, metric.unit) }}
          </div>
          <div class="metric-trend" v-if="metric.trend">
            <span 
              :class="['trend-indicator', metric.trend > 0 ? 'trend-up' : 'trend-down']"
            >
              {{ metric.trend > 0 ? '↗' : '↘' }} {{ Math.abs(metric.trend) }}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 滚动数据列表 -->
    <div class="scrolling-data" v-if="showScrollingData">
      <div class="scroll-header">
        <span class="scroll-title">实时数据流</span>
        <div class="scroll-indicator"></div>
      </div>
      <div class="scroll-content" ref="scrollContainer">
        <div 
          v-for="(item, index) in scrollingItems" 
          :key="index"
          class="scroll-item"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <span class="item-name">{{ item.name }}</span>
          <span class="item-value" :style="{ color: item.color }">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

interface Metric {
  label: string;
  value: number;
  unit?: string;
  color: string;
  icon: string;
  trend?: number;
  highlight?: boolean;
}

interface ScrollItem {
  name: string;
  value: string;
  color: string;
}

interface Props {
  metrics?: Metric[];
  scrollingData?: ScrollItem[];
  showScrollingData?: boolean;
  type?: 'grid' | 'list' | 'mixed';
}

const props = withDefaults(defineProps<Props>(), {
  metrics: () => [],
  scrollingData: () => [],
  showScrollingData: true,
  type: 'mixed'
});

// 默认指标数据
const defaultMetrics: Metric[] = [
  {
    label: '今日订单',
    value: 1234,
    unit: '单',
    color: '#00D4FF',
    icon: 'fas fa-box',
    trend: 12.5,
    highlight: true
  },
  {
    label: '处理中',
    value: 89,
    unit: '单',
    color: '#4ECDC4',
    icon: 'fas fa-clock',
    trend: -3.2
  },
  {
    label: '已完成',
    value: 1145,
    unit: '单',
    color: '#00FF88',
    icon: 'fas fa-check-circle',
    trend: 15.8
  },
  {
    label: '异常订单',
    value: 12,
    unit: '单',
    color: '#FF6B6B',
    icon: 'fas fa-exclamation-triangle',
    trend: -8.5
  }
];

// 默认滚动数据
const defaultScrollingData: ScrollItem[] = [
  { name: '武汉仓库', value: '98.5%', color: '#00FF88' },
  { name: '黄冈仓库', value: '96.2%', color: '#00D4FF' },
  { name: '天门仓库', value: '94.8%', color: '#4ECDC4' },
  { name: '深圳仓库', value: '99.1%', color: '#00FF88' },
  { name: '北京仓库', value: '97.3%', color: '#00D4FF' },
  { name: '上海仓库', value: '95.7%', color: '#4ECDC4' },
  { name: '广州仓库', value: '98.9%', color: '#00FF88' },
  { name: '成都仓库', value: '93.4%', color: '#FFEAA7' }
];

// 计算属性
const displayMetrics = computed(() => {
  return props.metrics.length > 0 ? props.metrics : defaultMetrics;
});

const scrollingItems = computed(() => {
  return props.scrollingData.length > 0 ? props.scrollingData : defaultScrollingData;
});

// 格式化数值
const formatValue = (value: number, unit?: string): string => {
  if (value >= 10000) {
    return `${(value / 10000).toFixed(1)}万${unit || ''}`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k${unit || ''}`;
  }
  return `${value}${unit || ''}`;
};

// 滚动容器引用
const scrollContainer = ref<HTMLElement>();

// 自动滚动
let scrollInterval: number | null = null;

const startAutoScroll = () => {
  if (!scrollContainer.value) return;
  
  scrollInterval = setInterval(() => {
    if (scrollContainer.value) {
      const container = scrollContainer.value;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      
      if (container.scrollTop >= scrollHeight - clientHeight) {
        container.scrollTop = 0;
      } else {
        container.scrollTop += 1;
      }
    }
  }, 50);
};

const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 生命周期
onMounted(() => {
  if (props.showScrollingData) {
    setTimeout(startAutoScroll, 1000);
  }
});

onUnmounted(() => {
  stopAutoScroll();
});
</script>

<style scoped>
.data-display-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  flex: 1;
}

.metric-item {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.6) 0%, rgba(14, 38, 92, 0.4) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 10px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.metric-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.6), transparent);
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.6);
}

.metric-highlight {
  border-color: rgba(0, 255, 136, 0.5);
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.2);
}

.metric-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
}

.icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.2;
  animation: pulse 2s infinite;
}

.metric-icon i {
  font-size: 18px;
  color: #7BDEFF;
  z-index: 1;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.9rem;
  color: #A0D8EF;
  margin-bottom: 5px;
  font-weight: 500;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 3px;
  text-shadow: 0 0 10px currentColor;
}

.metric-trend {
  font-size: 0.8rem;
}

.trend-indicator {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.trend-up {
  color: #00FF88;
  background: rgba(0, 255, 136, 0.1);
}

.trend-down {
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.1);
}

/* 滚动数据 */
.scrolling-data {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  height: 200px;
  backdrop-filter: blur(8px);
}

.scroll-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.scroll-title {
  font-size: 1rem;
  color: #7BDEFF;
  font-weight: 600;
}

.scroll-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00FF88;
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
  animation: pulse 2s infinite;
}

.scroll-content {
  height: calc(100% - 45px);
  overflow: hidden;
  padding: 10px 0;
}

.scroll-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.1);
  animation: slideInRight 0.5s ease-out;
}

.item-name {
  color: #A0D8EF;
  font-size: 0.9rem;
}

.item-value {
  font-weight: bold;
  font-size: 0.9rem;
  text-shadow: 0 0 8px currentColor;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
