<template>
  <div class="tech-panel-container">
    <!-- 顶部统计条 -->
    <div class="stats-bar">
      <div 
        v-for="(stat, index) in topStats" 
        :key="index"
        class="stat-block"
        :style="{ '--accent-color': stat.color }"
      >
        <div class="stat-icon">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ formatNumber(stat.value) }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
        <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
          <span class="trend-arrow">{{ stat.trend > 0 ? '↗' : '↘' }}</span>
          <span class="trend-value">{{ Math.abs(stat.trend) }}%</span>
        </div>
      </div>
    </div>

    <!-- 数据网格 -->
    <div class="data-grid">
      <!-- 左侧数据列表 -->
      <div class="data-list">
        <div class="list-header">
          <span class="header-title">{{ listTitle }}</span>
          <div class="header-indicator"></div>
        </div>
        <div class="list-content">
          <div 
            v-for="(item, index) in dataList" 
            :key="index"
            class="list-item"
            :class="{ 'item-highlight': item.highlight }"
          >
            <div class="item-rank">{{ String(index + 1).padStart(2, '0') }}</div>
            <div class="item-name">{{ item.name }}</div>
            <div class="item-value" :style="{ color: item.color }">
              {{ item.value }}
            </div>
            <div class="item-bar">
              <div 
                class="bar-fill" 
                :style="{ 
                  width: `${item.percentage}%`, 
                  backgroundColor: item.color,
                  boxShadow: `0 0 10px ${item.color}40`
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧圆形进度 -->
      <div class="progress-section">
        <div class="progress-title">系统状态</div>
        <div class="circular-progress">
          <svg class="progress-ring" width="120" height="120">
            <circle
              class="progress-ring-bg"
              stroke="rgba(0, 212, 255, 0.2)"
              stroke-width="8"
              fill="transparent"
              r="52"
              cx="60"
              cy="60"
            />
            <circle
              class="progress-ring-fill"
              stroke="#00D4FF"
              stroke-width="8"
              fill="transparent"
              r="52"
              cx="60"
              cy="60"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="progressOffset"
            />
          </svg>
          <div class="progress-text">
            <div class="progress-value">{{ systemHealth }}%</div>
            <div class="progress-label">健康度</div>
          </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="status-indicators">
          <div 
            v-for="(status, index) in statusList" 
            :key="index"
            class="status-item"
          >
            <div class="status-dot" :style="{ backgroundColor: status.color }"></div>
            <span class="status-name">{{ status.name }}</span>
            <span class="status-value">{{ status.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部实时数据流 -->
    <div class="data-stream" v-if="showDataStream">
      <div class="stream-header">
        <span class="stream-title">实时数据流</span>
        <div class="stream-controls">
          <button @click="toggleStream" class="stream-btn">
            {{ isStreaming ? '暂停' : '开始' }}
          </button>
        </div>
      </div>
      <div class="stream-content" ref="streamContainer">
        <div 
          v-for="(data, index) in streamData" 
          :key="data.id"
          class="stream-item"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <span class="stream-time">{{ data.time }}</span>
          <span class="stream-event">{{ data.event }}</span>
          <span class="stream-value" :style="{ color: data.color }">{{ data.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

interface StatBlock {
  label: string;
  value: number;
  icon: string;
  color: string;
  trend: number;
}

interface DataItem {
  name: string;
  value: string;
  percentage: number;
  color: string;
  highlight?: boolean;
}

interface StatusItem {
  name: string;
  value: string;
  color: string;
}

interface StreamData {
  id: string;
  time: string;
  event: string;
  value: string;
  color: string;
}

interface Props {
  topStats?: StatBlock[];
  dataList?: DataItem[];
  statusList?: StatusItem[];
  listTitle?: string;
  systemHealth?: number;
  showDataStream?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  topStats: () => [],
  dataList: () => [],
  statusList: () => [],
  listTitle: '数据排行',
  systemHealth: 96,
  showDataStream: true
});

// 默认数据
const defaultTopStats: StatBlock[] = [
  { label: '总订单', value: 12580, icon: 'fas fa-shopping-cart', color: '#00D4FF', trend: 12.5 },
  { label: '处理中', value: 234, icon: 'fas fa-clock', color: '#4ECDC4', trend: -3.2 },
  { label: '已完成', value: 12346, icon: 'fas fa-check', color: '#00FF88', trend: 15.8 },
  { label: '异常', value: 12, icon: 'fas fa-exclamation', color: '#FF6B6B', trend: -8.5 }
];

const defaultDataList: DataItem[] = [
  { name: '电子产品', value: '2,340', percentage: 85, color: '#00D4FF', highlight: true },
  { name: '服装鞋帽', value: '1,890', percentage: 68, color: '#4ECDC4' },
  { name: '食品饮料', value: '1,560', percentage: 56, color: '#00FF88' },
  { name: '家居用品', value: '1,200', percentage: 43, color: '#FFEAA7' },
  { name: '图书文具', value: '980', percentage: 35, color: '#DDA0DD' },
  { name: '其他商品', value: '720', percentage: 26, color: '#FF6B6B' }
];

const defaultStatusList: StatusItem[] = [
  { name: 'CPU使用率', value: '68%', color: '#00D4FF' },
  { name: '内存占用', value: '72%', color: '#4ECDC4' },
  { name: '网络延迟', value: '12ms', color: '#00FF88' },
  { name: '存储空间', value: '84%', color: '#FFEAA7' }
];

// 计算属性
const topStats = computed(() => props.topStats.length > 0 ? props.topStats : defaultTopStats);
const dataList = computed(() => props.dataList.length > 0 ? props.dataList : defaultDataList);
const statusList = computed(() => props.statusList.length > 0 ? props.statusList : defaultStatusList);

// 圆形进度条计算
const circumference = computed(() => 2 * Math.PI * 52);
const progressOffset = computed(() => {
  return circumference.value - (props.systemHealth / 100) * circumference.value;
});

// 数据流
const streamData = ref<StreamData[]>([]);
const isStreaming = ref(true);
const streamContainer = ref<HTMLElement>();

// 格式化数字
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return `${(num / 10000).toFixed(1)}万`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}k`;
  }
  return num.toString();
};

// 生成随机数据流
const generateStreamData = (): StreamData => {
  const events = ['订单创建', '支付完成', '发货通知', '签收确认', '退款处理'];
  const colors = ['#00D4FF', '#4ECDC4', '#00FF88', '#FFEAA7', '#FF6B6B'];
  const now = new Date();
  
  return {
    id: Math.random().toString(36).substr(2, 9),
    time: `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`,
    event: events[Math.floor(Math.random() * events.length)],
    value: `¥${(Math.random() * 1000 + 100).toFixed(2)}`,
    color: colors[Math.floor(Math.random() * colors.length)]
  };
};

// 添加数据流项目
const addStreamItem = () => {
  if (!isStreaming.value) return;
  
  streamData.value.unshift(generateStreamData());
  if (streamData.value.length > 20) {
    streamData.value.pop();
  }
};

// 切换数据流
const toggleStream = () => {
  isStreaming.value = !isStreaming.value;
};

// 定时器
let streamInterval: number | null = null;

// 生命周期
onMounted(() => {
  // 初始化数据流
  for (let i = 0; i < 5; i++) {
    streamData.value.push(generateStreamData());
  }
  
  // 开始数据流
  streamInterval = setInterval(addStreamItem, 2000);
});

onUnmounted(() => {
  if (streamInterval) {
    clearInterval(streamInterval);
  }
});
</script>

<style scoped>
.tech-panel-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  color: #ffffff;
}

/* 顶部统计条 */
.stats-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.stat-block {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.8) 0%, rgba(14, 38, 92, 0.6) 100%);
  border: 1px solid var(--accent-color, #00D4FF);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.stat-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color, #00D4FF), transparent);
}

.stat-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color, #00D4FF);
  font-size: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: bold;
  color: var(--accent-color, #00D4FF);
  text-shadow: 0 0 10px var(--accent-color, #00D4FF);
}

.stat-label {
  font-size: 0.8rem;
  color: #A0D8EF;
  margin-top: 2px;
}

.stat-trend {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.stat-trend.positive {
  color: #00FF88;
}

.stat-trend.negative {
  color: #FF6B6B;
}

/* 数据网格 */
.data-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 15px;
  flex: 1;
}

/* 数据列表 */
.data-list {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.header-title {
  font-size: 1rem;
  color: #7BDEFF;
  font-weight: 600;
}

.header-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00FF88;
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
  animation: pulse 2s infinite;
}

.list-content {
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.list-item {
  display: grid;
  grid-template-columns: 30px 1fr 80px 60px;
  align-items: center;
  gap: 10px;
  padding: 8px 10px;
  border-radius: 6px;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.list-item:hover {
  background: rgba(0, 212, 255, 0.1);
}

.item-highlight {
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.item-rank {
  font-size: 0.8rem;
  color: #7BDEFF;
  font-weight: bold;
  text-align: center;
}

.item-name {
  font-size: 0.9rem;
  color: #A0D8EF;
}

.item-value {
  font-size: 0.9rem;
  font-weight: bold;
  text-align: right;
}

.item-bar {
  height: 4px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.5s ease;
}

/* 进度部分 */
.progress-section {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  backdrop-filter: blur(8px);
}

.progress-title {
  font-size: 1rem;
  color: #7BDEFF;
  font-weight: 600;
}

.circular-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-fill {
  transition: stroke-dashoffset 0.5s ease;
  stroke-linecap: round;
  filter: drop-shadow(0 0 5px #00D4FF);
}

.progress-text {
  position: absolute;
  text-align: center;
}

.progress-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00D4FF;
  text-shadow: 0 0 10px #00D4FF;
}

.progress-label {
  font-size: 0.8rem;
  color: #A0D8EF;
  margin-top: 2px;
}

.status-indicators {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow: 0 0 8px currentColor;
}

.status-name {
  flex: 1;
  color: #A0D8EF;
}

.status-value {
  color: #7BDEFF;
  font-weight: bold;
}

/* 数据流 */
.data-stream {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.7) 0%, rgba(14, 38, 92, 0.5) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  height: 150px;
  backdrop-filter: blur(8px);
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.stream-title {
  font-size: 0.9rem;
  color: #7BDEFF;
  font-weight: 600;
}

.stream-btn {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00D4FF;
  color: #7BDEFF;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stream-btn:hover {
  background: rgba(0, 212, 255, 0.3);
}

.stream-content {
  height: calc(100% - 45px);
  overflow: hidden;
  padding: 8px 0;
}

.stream-item {
  display: grid;
  grid-template-columns: 60px 1fr 80px;
  align-items: center;
  gap: 10px;
  padding: 4px 15px;
  font-size: 0.8rem;
  animation: slideInLeft 0.5s ease-out;
}

.stream-time {
  color: #7BDEFF;
  font-family: monospace;
}

.stream-event {
  color: #A0D8EF;
}

.stream-value {
  text-align: right;
  font-weight: bold;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
