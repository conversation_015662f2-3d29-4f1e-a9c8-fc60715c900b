// 科技感ECharts主题配置
const THEME_COLOR = '#00BFFF' // 深天蓝
const BG_COLOR = 'rgba(10, 22, 52, 0.0)' // 透明背景

export const sciFiTheme = {
  // 主色调配置
  color: [
    '#00BFFF', // 深天蓝
    '#8A2BE2', // 蓝紫色
    '#32CD32', // 酸橙绿
    '#FF6347', // 番茄红
    '#FFD700', // 金色
    '#FF69B4', // 热粉色
    '#00CED1', // 深绿松石色
    '#9370DB', // 中紫色
    '#20B2AA', // 浅海绿色
    '#FF1493', // 深粉色
    '#00FF7F', // 春绿色
    '#DC143C', // 深红色
    '#00FFFF', // 青色
    '#FF4500', // 橙红色
    '#DA70D6', // 兰花紫
    '#ADFF2F', // 绿黄色
    '#FF8C00', // 深橙色
    '#9932CC', // 深兰花紫
    '#00FA9A', // 中春绿色
    '#FFB6C1', // 浅粉色
    '#87CEEB'  // 天蓝色
  ],

  // 背景色
  backgroundColor: BG_COLOR,

  // 文本样式
  textStyle: {
    color: '#FFFFFF',
    fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
  },

  // 标题
  title: {
    textStyle: {
      color: THEME_COLOR,
      fontSize: 18,
      fontWeight: 'bold',
      fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
    },
    subtextStyle: {
      color: '#CCCCCC',
      fontSize: 12,
      fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
    }
  },

  // 图例
  legend: {
    textStyle: {
      color: '#FFFFFF',
      fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
    }
  },

  // 工具提示
  tooltip: {
    backgroundColor: 'rgba(10, 22, 52, 0.9)',
    borderColor: THEME_COLOR,
    borderWidth: 1,
    textStyle: {
      color: '#FFFFFF',
      fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
    }
  },

  // 坐标轴
  categoryAxis: {
    axisLine: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisTick: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisLabel: {
      color: '#FFFFFF',
      fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(0, 191, 255, 0.2)'
      }
    }
  },

  valueAxis: {
    axisLine: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisTick: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisLabel: {
      color: '#FFFFFF',
      fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif'
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(0, 191, 255, 0.2)'
      }
    }
  },

  // 时间轴
  timeAxis: {
    axisLine: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisTick: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisLabel: {
      color: '#FFFFFF'
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(0, 191, 255, 0.2)'
      }
    }
  },

  // 对数轴
  logAxis: {
    axisLine: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisTick: {
      lineStyle: {
        color: THEME_COLOR
      }
    },
    axisLabel: {
      color: '#FFFFFF'
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(0, 191, 255, 0.2)'
      }
    }
  },

  // 线图
  line: {
    symbol: 'circle',
    symbolSize: 6,
    lineStyle: {
      width: 2
    },
    emphasis: {
      lineStyle: {
        width: 3
      }
    }
  },

  // 柱状图
  bar: {
    itemStyle: {
      borderRadius: [2, 2, 0, 0]
    }
  },

  // 饼图
  pie: {
    itemStyle: {
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.2)'
    },
    label: {
      color: '#FFFFFF'
    },
    labelLine: {
      lineStyle: {
        color: '#FFFFFF'
      }
    }
  },

  // 散点图
  scatter: {
    symbol: 'circle',
    symbolSize: 8
  },

  // 地图
  map: {
    itemStyle: {
      areaColor: 'rgba(0, 191, 255, 0.1)',
      borderColor: THEME_COLOR,
      borderWidth: 1
    },
    label: {
      color: '#FFFFFF'
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(0, 191, 255, 0.3)'
      },
      label: {
        color: THEME_COLOR
      }
    }
  },

  // 热力图
  heatmap: {
    itemStyle: {
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.2)'
    }
  },

  // 树图
  tree: {
    itemStyle: {
      color: THEME_COLOR,
      borderColor: '#FFFFFF',
      borderWidth: 1
    },
    lineStyle: {
      color: THEME_COLOR
    },
    label: {
      color: '#FFFFFF'
    }
  },

  // 矩形树图
  treemap: {
    itemStyle: {
      borderColor: '#FFFFFF',
      borderWidth: 1
    },
    label: {
      color: '#FFFFFF'
    }
  },

  // 旭日图
  sunburst: {
    itemStyle: {
      borderColor: '#FFFFFF',
      borderWidth: 1
    },
    label: {
      color: '#FFFFFF'
    }
  },

  // 平行坐标
  parallel: {
    lineStyle: {
      width: 2
    }
  },

  // 桑基图
  sankey: {
    nodeStyle: {
      color: THEME_COLOR
    },
    linkStyle: {
      color: 'rgba(0, 191, 255, 0.3)'
    },
    label: {
      color: '#FFFFFF'
    }
  },

  // 漏斗图
  funnel: {
    itemStyle: {
      borderColor: '#FFFFFF',
      borderWidth: 1
    },
    label: {
      color: '#FFFFFF'
    }
  },

  // 仪表盘
  gauge: {
    axisLine: {
      lineStyle: {
        color: [[0.2, '#32CD32'], [0.8, THEME_COLOR], [1, '#FF6347']],
        width: 8
      }
    },
    axisTick: {
      lineStyle: {
        color: '#FFFFFF'
      }
    },
    axisLabel: {
      color: '#FFFFFF'
    },
    splitLine: {
      lineStyle: {
        color: '#FFFFFF'
      }
    },
    pointer: {
      itemStyle: {
        color: THEME_COLOR
      }
    },
    title: {
      color: '#FFFFFF'
    },
    detail: {
      color: THEME_COLOR
    }
  },

  // 关系图
  graph: {
    itemStyle: {
      color: THEME_COLOR
    },
    lineStyle: {
      color: 'rgba(0, 191, 255, 0.5)'
    },
    label: {
      color: '#FFFFFF'
    }
  }
}
